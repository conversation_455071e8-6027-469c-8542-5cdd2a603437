#!/bin/bash

# =====================================================
# ATMA Backend - Complete Docker Setup Script
# =====================================================
# This script sets up the complete ATMA Backend system
# with Docker containers for all services
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="atma-backend"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to check prerequisites
check_prerequisites() {
    print_header "Checking Prerequisites"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    print_status "Docker is installed ✓"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    print_status "Docker Compose is installed ✓"
    
    # Check if Docker is running
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_status "Docker is running ✓"
    
    # Check available disk space (minimum 5GB)
    AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
    if [ $AVAILABLE_SPACE -lt 5242880 ]; then  # 5GB in KB
        print_warning "Less than 5GB disk space available. Consider freeing up space."
    else
        print_status "Sufficient disk space available ✓"
    fi
}

# Function to create directory structure
create_directories() {
    print_header "Creating Directory Structure"
    
    # Create main directories
    mkdir -p docker/postgres/init-scripts
    mkdir -p docker/rabbitmq
    mkdir -p docker/nginx
    mkdir -p data/{postgres,rabbitmq,redis}
    mkdir -p logs/{postgres,rabbitmq,redis,api-gateway,auth-service,archive-service,assessment-service,analysis-worker,notification-service}
    mkdir -p backups
    mkdir -p scripts
    
    # Set proper permissions
    chmod 755 data/{postgres,rabbitmq,redis}
    chmod 755 logs/*
    chmod 755 backups
    chmod +x scripts/*.sh 2>/dev/null || true
    
    print_status "Directory structure created ✓"
}

# Function to create Dockerfiles for each service
create_dockerfiles() {
    print_header "Creating Dockerfiles"
    
    # Base Dockerfile for Node.js services
    cat > Dockerfile.base << 'EOF'
FROM node:18-alpine

# Install security updates
RUN apk update && apk upgrade && apk add --no-cache curl

# Create app directory
WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Change ownership to nodejs user
RUN chown -R nodejs:nodejs /app
USER nodejs

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:${PORT:-3000}/health || exit 1

# Default command
CMD ["npm", "start"]
EOF

    # API Gateway Dockerfile
    cat > api-gateway/Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs && chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 3000

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:3000/health || exit 1

CMD ["npm", "start"]
EOF

    # Create similar Dockerfiles for other services
    for service in auth-service archive-service assessment-service analysis-worker notification-service; do
        if [ -d "$service" ]; then
            cp api-gateway/Dockerfile $service/Dockerfile
            # Update port in health check based on service
            case $service in
                auth-service) sed -i 's/3000/3001/g' $service/Dockerfile ;;
                archive-service) sed -i 's/3000/3002/g' $service/Dockerfile ;;
                assessment-service) sed -i 's/3000/3003/g' $service/Dockerfile ;;
                notification-service) sed -i 's/3000/3005/g' $service/Dockerfile ;;
                analysis-worker) 
                    # Analysis worker doesn't have HTTP endpoint, remove health check
                    sed -i '/HEALTHCHECK/d' $service/Dockerfile
                    sed -i '/curl/d' $service/Dockerfile
                    ;;
            esac
        fi
    done
    
    print_status "Dockerfiles created ✓"
}

# Function to create .dockerignore files
create_dockerignore() {
    print_header "Creating .dockerignore Files"
    
    # Global .dockerignore
    cat > .dockerignore << 'EOF'
node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.*
.nyc_output
coverage
.vscode
.idea
*.log
logs/
data/
backups/
docker/
scripts/
testing/
docs/
*.md
Dockerfile*
docker-compose*.yml
EOF

    # Copy to each service directory
    for service in api-gateway auth-service archive-service assessment-service analysis-worker notification-service; do
        if [ -d "$service" ]; then
            cp .dockerignore $service/.dockerignore
        fi
    done
    
    print_status ".dockerignore files created ✓"
}

# Function to setup environment files
setup_environment() {
    print_header "Setting up Environment Files"
    
    # Copy environment template
    if [ ! -f ".env" ]; then
        if [ -f ".env.docker" ]; then
            cp .env.docker .env
            print_status "Environment file created from template ✓"
        else
            print_warning ".env.docker template not found. You'll need to create .env manually."
        fi
    else
        print_status "Environment file already exists ✓"
    fi
    
    # Check for Google AI API Key
    if grep -q "your_google_ai_api_key_here" .env 2>/dev/null; then
        print_warning "Please update GOOGLE_AI_API_KEY in .env file with your actual API key"
        print_status "Get your API key from: https://makersuite.google.com/app/apikey"
    fi
}

# Function to run database setup
setup_database() {
    print_header "Setting up Database"
    
    if [ -f "scripts/setup-database.sh" ]; then
        print_status "Running database setup script..."
        chmod +x scripts/setup-database.sh
        ./scripts/setup-database.sh
    else
        print_warning "Database setup script not found. You'll need to setup database manually."
    fi
}

# Function to build Docker images
build_images() {
    print_header "Building Docker Images"
    
    print_status "Building all Docker images..."
    docker-compose build --parallel
    
    if [ $? -eq 0 ]; then
        print_status "All Docker images built successfully ✓"
    else
        print_error "Failed to build Docker images!"
        exit 1
    fi
}

# Function to start services
start_services() {
    print_header "Starting Services"
    
    print_status "Starting infrastructure services first..."
    docker-compose up -d postgres rabbitmq redis
    
    print_status "Waiting for infrastructure services to be ready..."
    sleep 30
    
    print_status "Starting application services..."
    docker-compose up -d
    
    print_status "Waiting for all services to be ready..."
    sleep 20
}

# Function to verify deployment
verify_deployment() {
    print_header "Verifying Deployment"
    
    # Check container status
    print_status "Checking container status..."
    docker-compose ps
    
    # Check health of services
    print_status "Checking service health..."
    
    # Wait for services to be fully ready
    sleep 10
    
    # Test API Gateway
    if curl -f http://localhost:3000/health > /dev/null 2>&1; then
        print_status "API Gateway is healthy ✓"
    else
        print_warning "API Gateway health check failed"
    fi
    
    # Test database connection
    if docker-compose exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
        print_status "PostgreSQL is healthy ✓"
    else
        print_warning "PostgreSQL health check failed"
    fi
    
    # Test RabbitMQ
    if docker-compose exec -T rabbitmq rabbitmq-diagnostics ping > /dev/null 2>&1; then
        print_status "RabbitMQ is healthy ✓"
    else
        print_warning "RabbitMQ health check failed"
    fi
}

# Function to show deployment summary
show_summary() {
    print_header "Deployment Summary"
    
    echo -e "${GREEN}ATMA Backend is now running! 🎉${NC}"
    echo ""
    echo -e "${GREEN}Service URLs:${NC}"
    echo "• API Gateway: http://localhost:3000"
    echo "• Auth Service: http://localhost:3001"
    echo "• Archive Service: http://localhost:3002"
    echo "• Assessment Service: http://localhost:3003"
    echo "• Notification Service: http://localhost:3005"
    echo ""
    echo -e "${GREEN}Management UIs:${NC}"
    echo "• RabbitMQ Management: http://localhost:15672"
    echo "  Username: atma_user"
    echo "  Password: atma_rabbitmq_password"
    echo ""
    echo -e "${GREEN}Database Connection:${NC}"
    echo "• Host: localhost"
    echo "• Port: 5432"
    echo "• Database: atma_db"
    echo "• Username: atma_user"
    echo "• Password: atma_secure_password_2024"
    echo ""
    echo -e "${GREEN}Useful Commands:${NC}"
    echo "• View logs: docker-compose logs -f [service-name]"
    echo "• Stop services: docker-compose down"
    echo "• Restart services: docker-compose restart"
    echo "• Scale worker: docker-compose up -d --scale analysis-worker=3"
    echo ""
    echo -e "${GREEN}Health Check:${NC}"
    echo "curl http://localhost:3000/health"
    echo ""
    echo -e "${YELLOW}Next Steps:${NC}"
    echo "1. Update GOOGLE_AI_API_KEY in .env file"
    echo "2. Test the API endpoints"
    echo "3. Check logs for any issues"
    echo "4. Setup monitoring and backups"
}

# Function to show help
show_help() {
    echo "ATMA Backend Docker Setup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  --skip-build        Skip building Docker images"
    echo "  --skip-db           Skip database setup"
    echo "  --dev               Setup for development environment"
    echo ""
    echo "Examples:"
    echo "  $0                  Full setup"
    echo "  $0 --skip-build     Setup without building images"
    echo "  $0 --dev            Development setup"
}

# Parse command line arguments
SKIP_BUILD=false
SKIP_DB=false
DEV_MODE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --skip-db)
            SKIP_DB=true
            shift
            ;;
        --dev)
            DEV_MODE=true
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_header "ATMA Backend - Docker Setup"
    
    check_prerequisites
    create_directories
    create_dockerfiles
    create_dockerignore
    setup_environment
    
    if [ "$SKIP_DB" = false ]; then
        setup_database
    fi
    
    if [ "$SKIP_BUILD" = false ]; then
        build_images
    fi
    
    start_services
    verify_deployment
    show_summary
    
    print_status "Docker setup completed successfully! 🎉"
}

# Run main function
main "$@"
