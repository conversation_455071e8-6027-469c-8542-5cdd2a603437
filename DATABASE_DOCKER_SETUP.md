# PostgreSQL Database Setup untuk Docker Environment

## 📋 Overview

Panduan lengkap untuk menyiapkan database PostgreSQL dalam environment Docker untuk proyek ATMA Backend, termasuk inisialisasi schema, data awal, dan konfigurasi yang optimal.

## 🗄️ Database Architecture

```
PostgreSQL Container (atma-postgres)
├── Database: atma_db
├── Schemas:
│   ├── public (shared data)
│   │   └── schools table
│   ├── auth (authentication service)
│   │   ├── users table
│   │   └── user_profiles table
│   ├── archive (archive service)
│   │   └── analysis_results table
│   └── assessment (assessment service)
│       └── assessment_submissions table
├── Extensions:
│   ├── uuid-ossp (UUID generation)
│   └── btree_gin (JSONB indexing)
└── Users:
    ├── atma_user (application user)
    └── postgres (admin user)
```

## 📁 File Structure untuk Database

```
docker/
└── postgres/
    ├── Dockerfile                    # Custom PostgreSQL image
    ├── postgresql.conf               # PostgreSQL configuration
    ├── pg_hba.conf                  # Authentication configuration
    └── init-scripts/                # Database initialization scripts
        ├── 01-create-database.sql   # Database dan user creation
        ├── 02-init-schema.sql       # Schema dan table creation
        ├── 03-init-data.sql         # Initial data insertion
        └── 04-create-indexes.sql    # Performance indexes
```

## 🐳 Docker Configuration

### 1. Custom PostgreSQL Dockerfile

```dockerfile
# docker/postgres/Dockerfile
FROM postgres:15-alpine

# Install additional extensions
RUN apk add --no-cache postgresql-contrib

# Copy configuration files
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Set proper permissions
RUN chmod +x /docker-entrypoint-initdb.d/*.sql

# Expose port
EXPOSE 5432

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1
```

### 2. PostgreSQL Configuration

```ini
# docker/postgres/postgresql.conf
# Basic Settings
listen_addresses = '*'
port = 5432
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'all'
log_min_duration_statement = 1000

# Performance
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Locale
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'
```

## 📝 Database Initialization Scripts

### 1. Database dan User Creation

```sql
-- docker/postgres/init-scripts/01-create-database.sql
-- Create database
CREATE DATABASE atma_db;

-- Create application user
CREATE USER atma_user WITH PASSWORD 'atma_secure_password_2024';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;

-- Connect to the database
\c atma_db;

-- Grant schema creation privileges
GRANT CREATE ON DATABASE atma_db TO atma_user;
ALTER USER atma_user CREATEDB;
```

### 2. Schema Initialization

```sql
-- docker/postgres/init-scripts/02-init-schema.sql
-- Connect to atma_db
\c atma_db;

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS btree_gin;

-- Create schemas
CREATE SCHEMA IF NOT EXISTS auth;
CREATE SCHEMA IF NOT EXISTS archive;
CREATE SCHEMA IF NOT EXISTS assessment;

-- Grant schema usage to application user
GRANT USAGE ON SCHEMA public TO atma_user;
GRANT USAGE ON SCHEMA auth TO atma_user;
GRANT USAGE ON SCHEMA archive TO atma_user;
GRANT USAGE ON SCHEMA assessment TO atma_user;

-- Grant all privileges on schemas
GRANT ALL ON SCHEMA auth TO atma_user;
GRANT ALL ON SCHEMA archive TO atma_user;
GRANT ALL ON SCHEMA assessment TO atma_user;

-- Grant all privileges on all tables in schemas
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA assessment TO atma_user;

-- Grant all privileges on all sequences in schemas
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA assessment TO atma_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON TABLES TO atma_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON SEQUENCES TO atma_user;
```

### 3. Initial Data

```sql
-- docker/postgres/init-scripts/03-init-data.sql
\c atma_db;

-- Insert sample schools
INSERT INTO public.schools (name, address, city, province) VALUES
('SMA Negeri 1 Jakarta', 'Jl. Budi Kemuliaan No. 6', 'Jakarta Pusat', 'DKI Jakarta'),
('SMA Negeri 3 Bandung', 'Jl. Belitung No. 8', 'Bandung', 'Jawa Barat'),
('SMA Negeri 1 Surabaya', 'Jl. Wijaya Kusuma No. 48', 'Surabaya', 'Jawa Timur'),
('SMA Negeri 2 Yogyakarta', 'Jl. Bener No. 30', 'Yogyakarta', 'DI Yogyakarta'),
('SMA Negeri 1 Medan', 'Jl. Mistar No. 41', 'Medan', 'Sumatera Utara');

-- Insert default admin user
INSERT INTO auth.users (
    id, 
    username, 
    email, 
    password_hash, 
    user_type, 
    is_active, 
    token_balance
) VALUES (
    uuid_generate_v4(),
    'superadmin',
    '<EMAIL>',
    '$2b$10$8K1p/a0dclxKoNqIfrHb2eUHiNNa6d4fMhEA6bMfLODAOtfSdZWO2', -- admin123
    'superadmin',
    true,
    1000
);

-- Insert admin profile
INSERT INTO auth.user_profiles (
    user_id,
    full_name,
    school_origin,
    gender
) VALUES (
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'System Administrator',
    'ATMA System',
    'male'
);
```

## 🔧 Docker Compose Configuration

```yaml
# docker-compose.yml (database section)
services:
  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    container_name: atma-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_admin_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - atma-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres

networks:
  atma-network:
    driver: bridge
```

## 🚀 Setup Commands

### 1. Persiapan Direktori

```bash
# Buat struktur direktori
mkdir -p docker/postgres/init-scripts
mkdir -p data/postgres
mkdir -p logs/postgres
mkdir -p backups

# Set permissions
chmod 755 data/postgres
chmod 755 logs/postgres
chmod 755 backups
```

### 2. Copy File Initialization

```bash
# Copy existing init-database.sql ke init scripts
cp init-database.sql docker/postgres/init-scripts/02-init-schema.sql

# Buat script tambahan
# (01-create-database.sql, 03-init-data.sql, 04-create-indexes.sql)
```

### 3. Start Database

```bash
# Start hanya database
docker-compose up -d postgres

# Check logs
docker-compose logs -f postgres

# Test connection
docker-compose exec postgres psql -U postgres -d atma_db -c "\dt auth.*"
```

## 🔍 Verification & Testing

### 1. Database Connection Test

```bash
# Test connection dari host
psql -h localhost -p 5432 -U atma_user -d atma_db

# Test dari container lain
docker-compose exec api-gateway npm run test:db
```

### 2. Schema Verification

```sql
-- Check schemas
\dn

-- Check tables in each schema
\dt public.*
\dt auth.*
\dt archive.*
\dt assessment.*

-- Check user permissions
\du
```

### 3. Data Verification

```sql
-- Check sample data
SELECT * FROM public.schools LIMIT 5;
SELECT username, email, user_type FROM auth.users;
SELECT COUNT(*) as total_users FROM auth.users;
```

## 🔒 Security Considerations

### 1. Password Management
- Gunakan strong passwords untuk semua users
- Store passwords dalam environment variables
- Rotate passwords secara berkala

### 2. Network Security
- Jangan expose port 5432 di production
- Gunakan internal Docker network
- Implement SSL/TLS untuk connections

### 3. Access Control
- Principle of least privilege
- Separate users untuk setiap service
- Regular audit user permissions

## 📊 Performance Tuning

### 1. Connection Pooling
```javascript
// Sequelize configuration
const sequelize = new Sequelize(database, username, password, {
  host: 'postgres',
  dialect: 'postgres',
  pool: {
    max: 25,
    min: 5,
    acquire: 60000,
    idle: 30000
  }
});
```

### 2. Index Optimization
- Monitor slow queries
- Add indexes untuk frequently queried columns
- Regular VACUUM dan ANALYZE

## 🔄 Backup & Recovery

### 1. Automated Backup Script

```bash
#!/bin/bash
# scripts/backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="atma_db_backup_$DATE.sql"

docker-compose exec postgres pg_dump -U postgres atma_db > ./backups/$BACKUP_FILE
gzip ./backups/$BACKUP_FILE

# Keep only last 7 days of backups
find ./backups -name "*.gz" -mtime +7 -delete
```

### 2. Recovery Process

```bash
# Restore from backup
gunzip -c ./backups/atma_db_backup_YYYYMMDD_HHMMSS.sql.gz | \
docker-compose exec -T postgres psql -U postgres atma_db
```

---

**Status**: 📋 Ready for Implementation
**Dependencies**: Docker, Docker Compose
**Estimated Setup Time**: 2-3 hours
