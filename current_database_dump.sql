--
-- PostgreSQL database dump
--

-- Dumped from database version 17.1
-- Dumped by pg_dump version 17.1

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: archive; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA archive;


ALTER SCHEMA archive OWNER TO postgres;

--
-- Name: assessment; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA assessment;


ALTER SCHEMA assessment OWNER TO postgres;

--
-- Name: auth; Type: SCHEMA; Schema: -; Owner: postgres
--

CREATE SCHEMA auth;


ALTER SCHEMA auth OWNER TO postgres;

--
-- Name: btree_gin; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS btree_gin WITH SCHEMA public;


--
-- Name: EXTENSION btree_gin; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION btree_gin IS 'support for indexing common datatypes in GIN';


--
-- Name: uuid-ossp; Type: EXTENSION; Schema: -; Owner: -
--

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA public;


--
-- Name: EXTENSION "uuid-ossp"; Type: COMMENT; Schema: -; Owner: 
--

COMMENT ON EXTENSION "uuid-ossp" IS 'generate universally unique identifiers (UUIDs)';


--
-- Name: cleanup_expired_idempotency_cache(); Type: FUNCTION; Schema: assessment; Owner: postgres
--

CREATE FUNCTION assessment.cleanup_expired_idempotency_cache() RETURNS integer
    LANGUAGE plpgsql
    AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM assessment.idempotency_cache 
    WHERE expires_at < NOW();
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$;


ALTER FUNCTION assessment.cleanup_expired_idempotency_cache() OWNER TO postgres;

--
-- Name: update_updated_at_column(); Type: FUNCTION; Schema: public; Owner: postgres
--

CREATE FUNCTION public.update_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$;


ALTER FUNCTION public.update_updated_at_column() OWNER TO postgres;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: analysis_jobs; Type: TABLE; Schema: archive; Owner: postgres
--

CREATE TABLE archive.analysis_jobs (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    job_id character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    status character varying(50) DEFAULT 'queued'::character varying NOT NULL,
    result_id uuid,
    error_message text,
    completed_at timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    assessment_name character varying(255) DEFAULT 'AI-Driven Talent Mapping'::character varying NOT NULL,
    priority integer DEFAULT 0 NOT NULL,
    retry_count integer DEFAULT 0 NOT NULL,
    max_retries integer DEFAULT 3 NOT NULL,
    processing_started_at timestamp with time zone,
    CONSTRAINT analysis_jobs_assessment_name_check CHECK (((assessment_name)::text = ANY ((ARRAY['AI-Driven Talent Mapping'::character varying, 'AI-Based IQ Test'::character varying, 'Custom Assessment'::character varying])::text[]))),
    CONSTRAINT analysis_jobs_status_check CHECK (((status)::text = ANY ((ARRAY['queued'::character varying, 'processing'::character varying, 'completed'::character varying, 'failed'::character varying])::text[])))
);


ALTER TABLE archive.analysis_jobs OWNER TO postgres;

--
-- Name: analysis_results; Type: TABLE; Schema: archive; Owner: postgres
--

CREATE TABLE archive.analysis_results (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    user_id uuid NOT NULL,
    assessment_data jsonb,
    persona_profile jsonb,
    status character varying(50) DEFAULT 'completed'::character varying NOT NULL,
    error_message text,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    assessment_name character varying(255) DEFAULT 'AI-Driven Talent Mapping'::character varying NOT NULL,
    CONSTRAINT analysis_results_assessment_name_check CHECK (((assessment_name)::text = ANY ((ARRAY['AI-Driven Talent Mapping'::character varying, 'AI-Based IQ Test'::character varying, 'Custom Assessment'::character varying])::text[]))),
    CONSTRAINT analysis_results_status_check CHECK (((status)::text = ANY ((ARRAY['completed'::character varying, 'processing'::character varying, 'failed'::character varying])::text[])))
);


ALTER TABLE archive.analysis_results OWNER TO postgres;

--
-- Name: idempotency_cache; Type: TABLE; Schema: assessment; Owner: postgres
--

CREATE TABLE assessment.idempotency_cache (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    idempotency_key character varying(255) NOT NULL,
    user_id uuid NOT NULL,
    request_hash character varying(64) NOT NULL,
    response_data jsonb NOT NULL,
    status_code integer NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    expires_at timestamp with time zone NOT NULL
);


ALTER TABLE assessment.idempotency_cache OWNER TO postgres;

--
-- Name: user_profiles; Type: TABLE; Schema: auth; Owner: postgres
--

CREATE TABLE auth.user_profiles (
    user_id uuid NOT NULL,
    full_name character varying(100),
    date_of_birth date,
    gender character varying(10),
    school_id integer,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT user_profiles_gender_check CHECK (((gender)::text = ANY ((ARRAY['male'::character varying, 'female'::character varying])::text[])))
);


ALTER TABLE auth.user_profiles OWNER TO postgres;

--
-- Name: users; Type: TABLE; Schema: auth; Owner: postgres
--

CREATE TABLE auth.users (
    id uuid DEFAULT public.uuid_generate_v4() NOT NULL,
    username character varying(100),
    email character varying(255) NOT NULL,
    password_hash character varying(255) NOT NULL,
    user_type character varying(20) DEFAULT 'user'::character varying NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    token_balance integer DEFAULT 0 NOT NULL,
    last_login timestamp with time zone,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    CONSTRAINT users_token_balance_check CHECK ((token_balance >= 0)),
    CONSTRAINT users_user_type_check CHECK (((user_type)::text = ANY ((ARRAY['user'::character varying, 'admin'::character varying, 'superadmin'::character varying, 'moderator'::character varying])::text[])))
);


ALTER TABLE auth.users OWNER TO postgres;

--
-- Name: schools; Type: TABLE; Schema: public; Owner: postgres
--

CREATE TABLE public.schools (
    id integer NOT NULL,
    name character varying(200) NOT NULL,
    address text,
    city character varying(100),
    province character varying(100),
    created_at timestamp with time zone DEFAULT now() NOT NULL
);


ALTER TABLE public.schools OWNER TO postgres;

--
-- Name: schools_id_seq; Type: SEQUENCE; Schema: public; Owner: postgres
--

CREATE SEQUENCE public.schools_id_seq
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE public.schools_id_seq OWNER TO postgres;

--
-- Name: schools_id_seq; Type: SEQUENCE OWNED BY; Schema: public; Owner: postgres
--

ALTER SEQUENCE public.schools_id_seq OWNED BY public.schools.id;


--
-- Name: schools id; Type: DEFAULT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schools ALTER COLUMN id SET DEFAULT nextval('public.schools_id_seq'::regclass);


--
-- Data for Name: analysis_jobs; Type: TABLE DATA; Schema: archive; Owner: postgres
--

COPY archive.analysis_jobs (id, job_id, user_id, status, result_id, error_message, completed_at, created_at, updated_at, assessment_name, priority, retry_count, max_retries, processing_started_at) FROM stdin;
044c7733-901d-45a7-bdb7-4b7f8b7d97a7	08617c3f-8426-4df5-9f0c-b64cef5aff98	87b6ed43-dc80-45e8-89d2-91f9de001d20	completed	dd85c2d2-7620-4208-9e95-d74955aa3e9e	\N	2025-07-21 14:57:35.621+07	2025-07-21 14:57:09.414+07	2025-07-21 14:57:35.617887+07	AI-Driven Talent Mapping	0	0	3	\N
7c8db5d3-69be-421c-9e0f-e240dc64d124	0ac95b75-a94e-4159-9d4a-1dac6002891b	75fd270a-a5d0-4e7b-ae9c-37eba88bbb3c	completed	5ee6afe3-dd9d-44a3-890b-40f53abfb425	\N	2025-07-21 14:57:35.624+07	2025-07-21 14:57:09.237+07	2025-07-21 14:57:35.619961+07	AI-Driven Talent Mapping	0	0	3	\N
b26d9841-cce0-43c4-8a3e-23f0b1df7b26	32bf4a7b-af56-421d-b97e-923e7814a1ad	c497ee64-0b1d-49fa-a520-2e3784cc4534	completed	c6d37d32-00e0-427f-a313-49e8d138b952	\N	2025-07-21 14:57:35.627+07	2025-07-21 14:57:09.149+07	2025-07-21 14:57:35.62632+07	AI-Driven Talent Mapping	0	0	3	\N
a79fec67-dab1-4221-9c94-9ccea662b46c	a143470b-ca15-4e8d-befd-f36912a4df17	0607d997-44cc-40a7-8388-44d41ab4fda6	completed	18073cbe-7eb6-49f1-8ee5-bc455ef83f67	\N	2025-07-21 14:57:35.65+07	2025-07-21 14:57:10.023+07	2025-07-21 14:57:35.646458+07	AI-Driven Talent Mapping	0	0	3	\N
ed06802a-4fcb-4373-8ac8-ba4fe7bfc160	29308d47-2275-45c8-8998-fae2e0ce8252	1d41270d-d20c-4ac8-b9d5-fa997b2eaa94	completed	12a9c0af-c52e-4433-b492-99b73a8096fe	\N	2025-07-21 14:57:35.655+07	2025-07-21 14:57:09.815+07	2025-07-21 14:57:35.649406+07	AI-Driven Talent Mapping	0	0	3	\N
f50714aa-7469-4192-b34a-34730d77b648	94be3016-23f7-4371-bb53-c0457861aa8b	b8d6a375-0397-43e7-badc-1216777307ee	completed	a3c09649-a134-4d58-9b10-62c24c50c98a	\N	2025-07-21 14:57:35.66+07	2025-07-21 14:57:09.618+07	2025-07-21 14:57:35.650525+07	AI-Driven Talent Mapping	0	0	3	\N
cb4da082-7ac7-4c1d-a444-26742aecee2e	9f1eb4e9-787a-42a5-bc73-31481ecdb4d6	9a343b74-57c6-43ce-93b0-80a69c10f575	completed	a83c3580-0b7a-485b-9eaf-c03d614c41ac	\N	2025-07-21 14:57:35.679+07	2025-07-21 14:57:10.643+07	2025-07-21 14:57:35.677002+07	AI-Driven Talent Mapping	0	0	3	\N
f4de04cb-7c9b-4836-b4f5-c3ef9660f643	97e33403-8070-4375-a4ff-7180bfc0428c	0bac9c76-32d8-4f64-a979-1959f1c691a6	completed	7bb49ec8-23b6-423c-bcdc-f623af8bd417	\N	2025-07-21 14:57:35.68+07	2025-07-21 14:57:10.415+07	2025-07-21 14:57:35.678512+07	AI-Driven Talent Mapping	0	0	3	\N
eab939ea-88c9-4d80-a2c5-c4a2efa39a94	17662eea-3856-40e9-8e2a-3ef4161b817a	200bb885-ff4b-4eba-a288-5a1a9e1a496e	completed	59468b0a-cc52-4310-8a0b-f4e1705ee009	\N	2025-07-21 14:57:35.681+07	2025-07-21 14:57:10.225+07	2025-07-21 14:57:35.67946+07	AI-Driven Talent Mapping	0	0	3	\N
fe345d6b-34f4-48f7-a996-280a9beb5993	09f802fb-8a53-4311-9f52-ece0d0fd2ed3	09a93837-237e-40ef-9dea-cefd093c7c9c	completed	7667f401-9ec6-4fb0-b3a9-b132d11dd782	\N	2025-07-21 14:57:35.69+07	2025-07-21 14:57:10.819+07	2025-07-21 14:57:35.690227+07	AI-Driven Talent Mapping	0	0	3	\N
\.


--
-- Data for Name: analysis_results; Type: TABLE DATA; Schema: archive; Owner: postgres
--

COPY archive.analysis_results (id, user_id, assessment_data, persona_profile, status, error_message, created_at, updated_at, assessment_name) FROM stdin;
c6d37d32-00e0-427f-a313-49e8d138b952	c497ee64-0b1d-49fa-a520-2e3784cc4534	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.535+07	2025-07-21 14:57:31.535+07	AI-Driven Talent Mapping
5ee6afe3-dd9d-44a3-890b-40f53abfb425	75fd270a-a5d0-4e7b-ae9c-37eba88bbb3c	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
dd85c2d2-7620-4208-9e95-d74955aa3e9e	87b6ed43-dc80-45e8-89d2-91f9de001d20	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
a3c09649-a134-4d58-9b10-62c24c50c98a	b8d6a375-0397-43e7-badc-1216777307ee	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
12a9c0af-c52e-4433-b492-99b73a8096fe	1d41270d-d20c-4ac8-b9d5-fa997b2eaa94	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
18073cbe-7eb6-49f1-8ee5-bc455ef83f67	0607d997-44cc-40a7-8388-44d41ab4fda6	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
59468b0a-cc52-4310-8a0b-f4e1705ee009	200bb885-ff4b-4eba-a288-5a1a9e1a496e	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
7bb49ec8-23b6-423c-bcdc-f623af8bd417	0bac9c76-32d8-4f64-a979-1959f1c691a6	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
a83c3580-0b7a-485b-9eaf-c03d614c41ac	9a343b74-57c6-43ce-93b0-80a69c10f575	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
7667f401-9ec6-4fb0-b3a9-b132d11dd782	09a93837-237e-40ef-9dea-cefd093c7c9c	{"ocean": {"openness": 80, "neuroticism": 30, "extraversion": 55, "agreeableness": 45, "conscientiousness": 65}, "viaIs": {"hope": 70, "love": 55, "zest": 60, "humor": 65, "bravery": 65, "honesty": 75, "fairness": 70, "humility": 50, "judgment": 70, "kindness": 68, "prudence": 65, "teamwork": 65, "curiosity": 78, "gratitude": 80, "creativity": 85, "leadership": 60, "forgiveness": 55, "perspective": 60, "perseverance": 70, "spirituality": 45, "loveOfLearning": 82, "selfRegulation": 70, "socialIntelligence": 72, "appreciationOfBeauty": 75}, "riasec": {"social": 50, "artistic": 60, "realistic": 75, "conventional": 55, "enterprising": 70, "investigative": 85}}	{"insights": ["Kembangkan keseimbangan antara struktur dan fleksibilitas dalam pendekatan kerja Anda", "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten", "Cari keseimbangan antara kolaborasi tim dan waktu untuk refleksi individual", "Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging", "Alokasikan waktu regular untuk creative exploration dan brainstorming sessions"], "archetype": "The Technical Problem Solver", "roleModel": ["Marie Curie", "Stephen Hawking", "Jane Goodall", "Elon Musk", "Steve Wozniak"], "strengths": ["Kemampuan praktis dan hands-on yang kuat", "Kemampuan analitis dan problem-solving yang excellent", "Keterbukaan terhadap ide baru dan pengalaman", "Kreativitas dan inovasi yang luar biasa", "Passion untuk pembelajaran berkelanjutan"], "weaknesses": ["Perlu pengembangan dalam area komunikasi interpersonal", "Dapat meningkatkan fleksibilitas dalam menghadapi perubahan", "Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif"], "shortSummary": "Anda adalah seorang The Technical Problem Solver dengan kekuatan utama di bidang investigative. Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.", "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.", "careerRecommendation": [{"careerName": "Research Scientist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Data Analyst", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}, {"careerName": "Psychologist", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Medical Doctor", "careerProspect": {"industryGrowth": "moderate", "jobAvailability": "moderate", "salaryPotential": "moderate", "skillDevelopment": "moderate", "careerProgression": "moderate"}}, {"careerName": "Software Engineer", "careerProspect": {"industryGrowth": "super high", "jobAvailability": "high", "salaryPotential": "high", "skillDevelopment": "super high", "careerProgression": "high"}}]}	completed	\N	2025-07-21 14:57:31.536+07	2025-07-21 14:57:31.536+07	AI-Driven Talent Mapping
\.


--
-- Data for Name: idempotency_cache; Type: TABLE DATA; Schema: assessment; Owner: postgres
--

COPY assessment.idempotency_cache (id, idempotency_key, user_id, request_hash, response_data, status_code, created_at, expires_at) FROM stdin;
\.


--
-- Data for Name: user_profiles; Type: TABLE DATA; Schema: auth; Owner: postgres
--

COPY auth.user_profiles (user_id, full_name, date_of_birth, gender, school_id, created_at, updated_at) FROM stdin;
c497ee64-0b1d-49fa-a520-2e3784cc4534	Mass Test User 1	1995-01-01	male	\N	2025-07-21 14:57:04.649+07	2025-07-21 14:57:04.649+07
75fd270a-a5d0-4e7b-ae9c-37eba88bbb3c	Mass Test User 2	1995-01-01	male	\N	2025-07-21 14:57:04.689+07	2025-07-21 14:57:04.69+07
87b6ed43-dc80-45e8-89d2-91f9de001d20	Mass Test User 3	1995-01-01	male	\N	2025-07-21 14:57:04.862+07	2025-07-21 14:57:04.862+07
b8d6a375-0397-43e7-badc-1216777307ee	Mass Test User 4	1995-01-01	male	\N	2025-07-21 14:57:05.046+07	2025-07-21 14:57:05.046+07
1d41270d-d20c-4ac8-b9d5-fa997b2eaa94	Mass Test User 5	1995-01-01	male	\N	2025-07-21 14:57:05.257+07	2025-07-21 14:57:05.257+07
0607d997-44cc-40a7-8388-44d41ab4fda6	Mass Test User 6	1995-01-01	male	\N	2025-07-21 14:57:05.466+07	2025-07-21 14:57:05.466+07
200bb885-ff4b-4eba-a288-5a1a9e1a496e	Mass Test User 7	1995-01-01	male	\N	2025-07-21 14:57:05.66+07	2025-07-21 14:57:05.66+07
0bac9c76-32d8-4f64-a979-1959f1c691a6	Mass Test User 8	1995-01-01	male	\N	2025-07-21 14:57:05.864+07	2025-07-21 14:57:05.864+07
9a343b74-57c6-43ce-93b0-80a69c10f575	Mass Test User 9	1995-01-01	male	\N	2025-07-21 14:57:06.063+07	2025-07-21 14:57:06.063+07
09a93837-237e-40ef-9dea-cefd093c7c9c	Mass Test User 10	1995-01-01	male	\N	2025-07-21 14:57:06.263+07	2025-07-21 14:57:06.263+07
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: auth; Owner: postgres
--

COPY auth.users (id, username, email, password_hash, user_type, is_active, token_balance, last_login, created_at, updated_at) FROM stdin;
0bac9c76-32d8-4f64-a979-1959f1c691a6	massuser17530846257268407	<EMAIL>	$2b$10$0r4o2lWPE6po3ifhHg/PVewfpUbLqdQScVC.Pn6QOmZ/rnSZTk.Zi	user	t	2	2025-07-21 14:57:05.849+07	2025-07-21 14:57:05.783+07	2025-07-21 14:57:10.403241+07
9a343b74-57c6-43ce-93b0-80a69c10f575	massuser175308462592898682	<EMAIL>	$2b$10$1WZz.lbAlkmWzjlvJEr7OuzIY0fikT1WVPsdIkmhD.Y8po3t.ZY32	user	t	2	2025-07-21 14:57:06.051+07	2025-07-21 14:57:05.988+07	2025-07-21 14:57:10.61594+07
09a93837-237e-40ef-9dea-cefd093c7c9c	massuser1753084626116102202	<EMAIL>	$2b$10$//hKnO4MKH.taCJ31YSSpuUKfBOCTWzXsYYrXeoFAwhBgsntkYRU2	user	t	2	2025-07-21 14:57:06.25+07	2025-07-21 14:57:06.186+07	2025-07-21 14:57:10.80911+07
c497ee64-0b1d-49fa-a520-2e3784cc4534	massuser175308462431511115	<EMAIL>	$2b$10$sRICvZ3m83ISx5Rlx3QuNeRNVbE9xmlnpBtqVB59NGyCp9MhluVtO	user	t	2	2025-07-21 14:57:04.624+07	2025-07-21 14:57:04.532+07	2025-07-21 14:57:09.075093+07
75fd270a-a5d0-4e7b-ae9c-37eba88bbb3c	massuser175308462452726213	<EMAIL>	$2b$10$op4NhYSDSRy1gbZ5YhJMV.0tVTCw22Nc7jLGNnur7yTdxjX9mqLXC	user	t	2	2025-07-21 14:57:04.673+07	2025-07-21 14:57:04.604+07	2025-07-21 14:57:09.224527+07
87b6ed43-dc80-45e8-89d2-91f9de001d20	massuser175308462472837231	<EMAIL>	$2b$10$dFn/CUOZ0F/iH.neAADCuOmOGNThKNfHh.5xE1YBl5S4l0X7IemhS	user	t	2	2025-07-21 14:57:04.851+07	2025-07-21 14:57:04.784+07	2025-07-21 14:57:09.404528+07
b8d6a375-0397-43e7-badc-1216777307ee	massuser175308462491541590	<EMAIL>	$2b$10$Fzn4HV/deWwjZLc7AzG2/u5wint4Wl0XUGpL0L79D64dFuuVch3vO	user	t	2	2025-07-21 14:57:05.036+07	2025-07-21 14:57:04.975+07	2025-07-21 14:57:09.602966+07
1d41270d-d20c-4ac8-b9d5-fa997b2eaa94	massuser175308462511852826	<EMAIL>	$2b$10$fOV3CggRjNVBTLZvVOftFOQTvrkHCwA0MuGHit3yw8SRVI/bLe4EO	user	t	2	2025-07-21 14:57:05.246+07	2025-07-21 14:57:05.182+07	2025-07-21 14:57:09.807282+07
0607d997-44cc-40a7-8388-44d41ab4fda6	massuser175308462531963147	<EMAIL>	$2b$10$/PPRmD9hb3kN3xVz9zxOxOl4KflfFET8RuWfwtA/LpGaH3KhlEz1q	user	t	2	2025-07-21 14:57:05.449+07	2025-07-21 14:57:05.376+07	2025-07-21 14:57:10.013279+07
200bb885-ff4b-4eba-a288-5a1a9e1a496e	massuser175308462552279641	<EMAIL>	$2b$10$qjVg3TftluLf5ar4WIuWle6F7cB.R5z5hlp9uRI3IqPvRn94ni4MG	user	t	2	2025-07-21 14:57:05.649+07	2025-07-21 14:57:05.583+07	2025-07-21 14:57:10.216455+07
\.


--
-- Data for Name: schools; Type: TABLE DATA; Schema: public; Owner: postgres
--

COPY public.schools (id, name, address, city, province, created_at) FROM stdin;
\.


--
-- Name: schools_id_seq; Type: SEQUENCE SET; Schema: public; Owner: postgres
--

SELECT pg_catalog.setval('public.schools_id_seq', 1, false);


--
-- Name: analysis_jobs analysis_jobs_job_id_key; Type: CONSTRAINT; Schema: archive; Owner: postgres
--

ALTER TABLE ONLY archive.analysis_jobs
    ADD CONSTRAINT analysis_jobs_job_id_key UNIQUE (job_id);


--
-- Name: analysis_jobs analysis_jobs_pkey; Type: CONSTRAINT; Schema: archive; Owner: postgres
--

ALTER TABLE ONLY archive.analysis_jobs
    ADD CONSTRAINT analysis_jobs_pkey PRIMARY KEY (id);


--
-- Name: analysis_results analysis_results_pkey; Type: CONSTRAINT; Schema: archive; Owner: postgres
--

ALTER TABLE ONLY archive.analysis_results
    ADD CONSTRAINT analysis_results_pkey PRIMARY KEY (id);


--
-- Name: idempotency_cache idempotency_cache_idempotency_key_key; Type: CONSTRAINT; Schema: assessment; Owner: postgres
--

ALTER TABLE ONLY assessment.idempotency_cache
    ADD CONSTRAINT idempotency_cache_idempotency_key_key UNIQUE (idempotency_key);


--
-- Name: idempotency_cache idempotency_cache_pkey; Type: CONSTRAINT; Schema: assessment; Owner: postgres
--

ALTER TABLE ONLY assessment.idempotency_cache
    ADD CONSTRAINT idempotency_cache_pkey PRIMARY KEY (id);


--
-- Name: user_profiles user_profiles_pkey; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.user_profiles
    ADD CONSTRAINT user_profiles_pkey PRIMARY KEY (user_id);


--
-- Name: users users_email_key; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_email_key UNIQUE (email);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: users users_username_key; Type: CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.users
    ADD CONSTRAINT users_username_key UNIQUE (username);


--
-- Name: schools schools_pkey; Type: CONSTRAINT; Schema: public; Owner: postgres
--

ALTER TABLE ONLY public.schools
    ADD CONSTRAINT schools_pkey PRIMARY KEY (id);


--
-- Name: idx_analysis_jobs_assessment_name; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_assessment_name ON archive.analysis_jobs USING btree (assessment_name);


--
-- Name: idx_analysis_jobs_created_at; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_created_at ON archive.analysis_jobs USING btree (created_at);


--
-- Name: idx_analysis_jobs_job_id; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE UNIQUE INDEX idx_analysis_jobs_job_id ON archive.analysis_jobs USING btree (job_id);


--
-- Name: idx_analysis_jobs_queue_processing; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_queue_processing ON archive.analysis_jobs USING btree (status, priority, created_at);


--
-- Name: idx_analysis_jobs_retry_logic; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_retry_logic ON archive.analysis_jobs USING btree (status, retry_count, max_retries);


--
-- Name: idx_analysis_jobs_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_status ON archive.analysis_jobs USING btree (status);


--
-- Name: idx_analysis_jobs_user_id; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_user_id ON archive.analysis_jobs USING btree (user_id);


--
-- Name: idx_analysis_jobs_user_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_user_status ON archive.analysis_jobs USING btree (user_id, status);


--
-- Name: idx_analysis_jobs_user_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_jobs_user_status_created ON archive.analysis_jobs USING btree (user_id, status, created_at);


--
-- Name: idx_analysis_results_archetype_assessment; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_archetype_assessment ON archive.analysis_results USING btree (((persona_profile ->> 'archetype'::text)), assessment_name);


--
-- Name: idx_analysis_results_archetype_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_archetype_status_created ON archive.analysis_results USING btree (((persona_profile ->> 'archetype'::text)), status, created_at);


--
-- Name: idx_analysis_results_assessment_data_gin; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_assessment_data_gin ON archive.analysis_results USING gin (assessment_data);


--
-- Name: idx_analysis_results_assessment_name; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_assessment_name ON archive.analysis_results USING btree (assessment_name);


--
-- Name: idx_analysis_results_assessment_name_optimized; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_assessment_name_optimized ON archive.analysis_results USING btree (assessment_name);


--
-- Name: idx_analysis_results_created_at; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_created_at ON archive.analysis_results USING btree (created_at);


--
-- Name: idx_analysis_results_persona_profile_gin; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_persona_profile_gin ON archive.analysis_results USING gin (persona_profile);


--
-- Name: idx_analysis_results_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_status ON archive.analysis_results USING btree (status);


--
-- Name: idx_analysis_results_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_status_created ON archive.analysis_results USING btree (status, created_at);


--
-- Name: idx_analysis_results_user_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_created ON archive.analysis_results USING btree (user_id, created_at);


--
-- Name: idx_analysis_results_user_id; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_id ON archive.analysis_results USING btree (user_id);


--
-- Name: idx_analysis_results_user_status; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_status ON archive.analysis_results USING btree (user_id, status);


--
-- Name: idx_analysis_results_user_status_created; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_analysis_results_user_status_created ON archive.analysis_results USING btree (user_id, status, created_at);


--
-- Name: idx_persona_profile_archetype; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_archetype ON archive.analysis_results USING gin (((persona_profile ->> 'archetype'::text)));


--
-- Name: idx_persona_profile_career_recommendations; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_career_recommendations ON archive.analysis_results USING gin (((persona_profile -> 'careerRecommendations'::text)));


--
-- Name: idx_persona_profile_ocean; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_ocean ON archive.analysis_results USING gin (((persona_profile -> 'ocean'::text)));


--
-- Name: idx_persona_profile_riasec; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_riasec ON archive.analysis_results USING gin (((persona_profile -> 'riasec'::text)));


--
-- Name: idx_persona_profile_risk_tolerance; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_risk_tolerance ON archive.analysis_results USING gin (((persona_profile ->> 'riskTolerance'::text)));


--
-- Name: idx_persona_profile_strengths; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_strengths ON archive.analysis_results USING gin (((persona_profile -> 'strengths'::text)));


--
-- Name: idx_persona_profile_weaknesses; Type: INDEX; Schema: archive; Owner: postgres
--

CREATE INDEX idx_persona_profile_weaknesses ON archive.analysis_results USING gin (((persona_profile -> 'weaknesses'::text)));


--
-- Name: idx_idempotency_cache_expires_at; Type: INDEX; Schema: assessment; Owner: postgres
--

CREATE INDEX idx_idempotency_cache_expires_at ON assessment.idempotency_cache USING btree (expires_at);


--
-- Name: idx_idempotency_cache_key; Type: INDEX; Schema: assessment; Owner: postgres
--

CREATE INDEX idx_idempotency_cache_key ON assessment.idempotency_cache USING btree (idempotency_key);


--
-- Name: idx_idempotency_cache_user_id; Type: INDEX; Schema: assessment; Owner: postgres
--

CREATE INDEX idx_idempotency_cache_user_id ON assessment.idempotency_cache USING btree (user_id);


--
-- Name: idx_user_profiles_created_at; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_created_at ON auth.user_profiles USING btree (created_at);


--
-- Name: idx_user_profiles_school_id; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_school_id ON auth.user_profiles USING btree (school_id);


--
-- Name: idx_user_profiles_school_id_idx; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_school_id_idx ON auth.user_profiles USING btree (school_id);


--
-- Name: idx_user_profiles_user_id; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_user_profiles_user_id ON auth.user_profiles USING btree (user_id);


--
-- Name: idx_users_admin_lookup; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_admin_lookup ON auth.users USING btree (user_type, is_active, email);


--
-- Name: idx_users_created_at; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_created_at ON auth.users USING btree (created_at);


--
-- Name: idx_users_email; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE UNIQUE INDEX idx_users_email ON auth.users USING btree (email);


--
-- Name: idx_users_is_active; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_is_active ON auth.users USING btree (is_active);


--
-- Name: idx_users_user_type; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE INDEX idx_users_user_type ON auth.users USING btree (user_type);


--
-- Name: idx_users_username; Type: INDEX; Schema: auth; Owner: postgres
--

CREATE UNIQUE INDEX idx_users_username ON auth.users USING btree (username) WHERE (username IS NOT NULL);


--
-- Name: idx_schools_city; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_city ON public.schools USING btree (city);


--
-- Name: idx_schools_created_at; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_created_at ON public.schools USING btree (created_at);


--
-- Name: idx_schools_full_info; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_full_info ON public.schools USING btree (name, city, province);


--
-- Name: idx_schools_location; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_location ON public.schools USING btree (province, city);


--
-- Name: idx_schools_name; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_name ON public.schools USING btree (name);


--
-- Name: idx_schools_province; Type: INDEX; Schema: public; Owner: postgres
--

CREATE INDEX idx_schools_province ON public.schools USING btree (province);


--
-- Name: analysis_jobs update_analysis_jobs_updated_at; Type: TRIGGER; Schema: archive; Owner: postgres
--

CREATE TRIGGER update_analysis_jobs_updated_at BEFORE UPDATE ON archive.analysis_jobs FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: analysis_results update_analysis_results_updated_at; Type: TRIGGER; Schema: archive; Owner: postgres
--

CREATE TRIGGER update_analysis_results_updated_at BEFORE UPDATE ON archive.analysis_results FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_profiles update_user_profiles_updated_at; Type: TRIGGER; Schema: auth; Owner: postgres
--

CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON auth.user_profiles FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: users update_users_updated_at; Type: TRIGGER; Schema: auth; Owner: postgres
--

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON auth.users FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();


--
-- Name: user_profiles user_profiles_school_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.user_profiles
    ADD CONSTRAINT user_profiles_school_id_fkey FOREIGN KEY (school_id) REFERENCES public.schools(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: auth; Owner: postgres
--

ALTER TABLE ONLY auth.user_profiles
    ADD CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES auth.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: SCHEMA archive; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA archive TO PUBLIC;


--
-- Name: SCHEMA assessment; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA assessment TO PUBLIC;


--
-- Name: SCHEMA auth; Type: ACL; Schema: -; Owner: postgres
--

GRANT USAGE ON SCHEMA auth TO PUBLIC;


--
-- Name: SCHEMA public; Type: ACL; Schema: -; Owner: pg_database_owner
--

GRANT ALL ON SCHEMA public TO atma_user;


--
-- Name: TABLE analysis_jobs; Type: ACL; Schema: archive; Owner: postgres
--

GRANT ALL ON TABLE archive.analysis_jobs TO PUBLIC;


--
-- Name: TABLE analysis_results; Type: ACL; Schema: archive; Owner: postgres
--

GRANT ALL ON TABLE archive.analysis_results TO PUBLIC;


--
-- Name: TABLE idempotency_cache; Type: ACL; Schema: assessment; Owner: postgres
--

GRANT SELECT ON TABLE assessment.idempotency_cache TO atma_user;


--
-- Name: TABLE user_profiles; Type: ACL; Schema: auth; Owner: postgres
--

GRANT ALL ON TABLE auth.user_profiles TO PUBLIC;


--
-- Name: TABLE users; Type: ACL; Schema: auth; Owner: postgres
--

GRANT ALL ON TABLE auth.users TO PUBLIC;


--
-- Name: TABLE schools; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON TABLE public.schools TO atma_user;
GRANT ALL ON TABLE public.schools TO PUBLIC;


--
-- Name: SEQUENCE schools_id_seq; Type: ACL; Schema: public; Owner: postgres
--

GRANT ALL ON SEQUENCE public.schools_id_seq TO atma_user;
GRANT ALL ON SEQUENCE public.schools_id_seq TO PUBLIC;


--
-- Name: DEFAULT PRIVILEGES FOR SEQUENCES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON SEQUENCES TO atma_user;


--
-- Name: DEFAULT PRIVILEGES FOR TABLES; Type: DEFAULT ACL; Schema: public; Owner: postgres
--

ALTER DEFAULT PRIVILEGES FOR ROLE postgres IN SCHEMA public GRANT ALL ON TABLES TO atma_user;


--
-- PostgreSQL database dump complete
--

