# 🔄 Menggunakan Database yang Sedang Berjalan untuk Docker

## 📋 Overview

Panduan ini membantu Anda menggunakan database PostgreSQL yang sedang berjalan (yang sudah dimodifikasi) untuk setup Docker, menggantikan file `init-database.sql` yang sudah tidak relevan.

## 🚀 Quick Start (Otomatis)

### Opsi 1: Update Otomatis <PERSON>
```bash
# 1. Export database yang sedang berjalan
chmod +x scripts/export-current-database.sh
./scripts/export-current-database.sh

# 2. Update setup Docker dengan database terbaru
chmod +x scripts/update-docker-database.sh
./scripts/update-docker-database.sh

# 3. Setup Docker dengan database yang sudah diupdate
./scripts/setup-docker.sh
```

### Opsi 2: Step by Step Manual
```bash
# 1. Export database
./scripts/export-current-database.sh

# 2. Check hasil export
ls database_exports/

# 3. Update Docker setup
./scripts/update-docker-database.sh

# 4. Test database setup
docker-compose -f docker-compose.db.yml up -d postgres
```

## 🔧 Manual Export Database

### A. Export Lengkap (Schema + Data)
```bash
# Export seluruh database
pg_dump -h localhost -p 5432 -U atma_user -d atma_db > current_database.sql

# Atau jika menggunakan postgres user
pg_dump -h localhost -p 5432 -U postgres -d atma_db > current_database.sql
```

### B. Export Terpisah
```bash
# Hanya schema
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --schema-only > schema_only.sql

# Hanya data
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --data-only > data_only.sql
```

### C. Export Per Schema
```bash
# Schema auth
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --schema=auth > auth_schema.sql

# Schema archive
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --schema=archive > archive_schema.sql

# Schema assessment
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --schema=assessment > assessment_schema.sql

# Schema public
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --schema=public > public_schema.sql
```

## 📁 Manual Setup Docker Files

### 1. Buat Direktori
```bash
mkdir -p docker/postgres/init-scripts
mkdir -p database_exports
```

### 2. Copy Database Export
```bash
# Copy hasil export ke init scripts
cp current_database.sql docker/postgres/init-scripts/02-restore-database.sql
```

### 3. Buat Script Inisialisasi
```bash
# Buat script create database
cat > docker/postgres/init-scripts/01-create-database.sql << 'EOF'
-- Create database
CREATE DATABASE atma_db;

-- Create application user
CREATE USER atma_user WITH PASSWORD 'atma_secure_password_2024';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;

-- Connect to the database
\c atma_db;

-- Grant schema creation privileges
GRANT CREATE ON DATABASE atma_db TO atma_user;
ALTER USER atma_user CREATEDB;
EOF
```

### 4. Tambah Script Permissions
```bash
# Buat script permissions
cat > docker/postgres/init-scripts/03-set-permissions.sql << 'EOF'
\c atma_db;

-- Grant permissions untuk semua schema yang ada
DO $$
DECLARE
    schema_name text;
BEGIN
    FOR schema_name IN SELECT nspname FROM pg_namespace WHERE nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast') LOOP
        EXECUTE 'GRANT USAGE ON SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'GRANT ALL ON SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name) || ' GRANT ALL ON TABLES TO atma_user';
        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name) || ' GRANT ALL ON SEQUENCES TO atma_user';
    END LOOP;
END $$;
EOF
```

## 🔍 Verifikasi Database Export

### Check Database Info
```bash
# Jalankan script info
./scripts/export-current-database.sh --info
```

### Manual Check
```sql
-- Connect ke database
psql -h localhost -p 5432 -U atma_user -d atma_db

-- Check schemas
\dn

-- Check tables per schema
\dt public.*
\dt auth.*
\dt archive.*
\dt assessment.*

-- Check data counts
SELECT 'public.schools' as table_name, COUNT(*) as count FROM public.schools
UNION ALL
SELECT 'auth.users', COUNT(*) FROM auth.users
UNION ALL
SELECT 'auth.user_profiles', COUNT(*) FROM auth.user_profiles;

-- Check specific data
SELECT id, username, email, user_type FROM auth.users LIMIT 5;
```

## 🐳 Test Docker Setup

### 1. Test Database Container
```bash
# Start hanya database
docker-compose -f docker-compose.db.yml up -d postgres

# Check logs
docker-compose -f docker-compose.db.yml logs -f postgres

# Wait for ready
sleep 30
```

### 2. Verify Database
```bash
# Check if database exists
docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -lqt | grep atma_db

# Check user
docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d atma_db -c "\du"

# Check schemas
docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d atma_db -c "\dn"

# Check tables
docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d atma_db -c "\dt auth.*"
```

### 3. Test Application User
```bash
# Test connection as atma_user
docker-compose -f docker-compose.db.yml exec postgres psql -U atma_user -d atma_db -c "SELECT current_user, current_database();"

# Test permissions
docker-compose -f docker-compose.db.yml exec postgres psql -U atma_user -d atma_db -c "SELECT COUNT(*) FROM auth.users;"
```

## 🔧 Troubleshooting

### Database Connection Issues
```bash
# Check if database is running
pg_isready -h localhost -p 5432 -U atma_user -d atma_db

# Check connection settings
psql -h localhost -p 5432 -U atma_user -d atma_db -c "SELECT version();"
```

### Export Issues
```bash
# Check database exists
psql -h localhost -p 5432 -U postgres -l | grep atma_db

# Check user permissions
psql -h localhost -p 5432 -U postgres -c "\du"

# Try with different user
pg_dump -h localhost -p 5432 -U postgres -d atma_db > database_dump.sql
```

### Docker Issues
```bash
# Check Docker files
ls -la docker/postgres/init-scripts/

# Check file permissions
chmod +x docker/postgres/init-scripts/*.sql

# Rebuild container
docker-compose -f docker-compose.db.yml down
docker-compose -f docker-compose.db.yml build --no-cache postgres
docker-compose -f docker-compose.db.yml up -d postgres
```

## 📊 Database Migration Strategy

### Untuk Perubahan Schema Besar
```bash
# 1. Export schema saja
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --schema-only > new_schema.sql

# 2. Export data saja
pg_dump -h localhost -p 5432 -U atma_user -d atma_db --data-only > new_data.sql

# 3. Buat init scripts terpisah
cp new_schema.sql docker/postgres/init-scripts/02-schema.sql
cp new_data.sql docker/postgres/init-scripts/03-data.sql
```

### Untuk Update Berkala
```bash
# 1. Backup database lama
./scripts/backup-database.sh

# 2. Export database terbaru
./scripts/export-current-database.sh

# 3. Update Docker setup
./scripts/update-docker-database.sh

# 4. Test setup baru
docker-compose -f docker-compose.db.yml up -d postgres
```

## 📋 File Structure Hasil

```
atma-backend/
├── database_exports/                    # Export results
│   ├── complete_database_TIMESTAMP.sql  # Full database
│   ├── schema_only_TIMESTAMP.sql       # Schema only
│   ├── data_only_TIMESTAMP.sql         # Data only
│   ├── schema_auth_TIMESTAMP.sql       # Auth schema
│   ├── schema_archive_TIMESTAMP.sql    # Archive schema
│   └── schema_assessment_TIMESTAMP.sql # Assessment schema
├── docker/postgres/init-scripts/       # Docker initialization
│   ├── 01-create-database.sql          # Database creation
│   ├── 02-restore-database.sql         # Current database
│   └── 03-set-permissions.sql          # User permissions
├── init-database.sql.backup.TIMESTAMP  # Backup of old file
└── init-database.sql                   # Updated with current data
```

## ✅ Checklist

- [ ] Database sedang berjalan dan accessible
- [ ] Export database berhasil
- [ ] Docker init scripts dibuat
- [ ] Test database container berhasil
- [ ] Verifikasi schema dan data
- [ ] Test application user permissions
- [ ] Backup file lama tersimpan

---

**Status**: ✅ Ready to Use
**Compatibility**: PostgreSQL 12+, Docker 20+
**Last Updated**: 2024
