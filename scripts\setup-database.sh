#!/bin/bash

# =====================================================
# ATMA Backend - Database Setup Script for Docker
# =====================================================
# This script sets up PostgreSQL database for ATMA Backend
# in Docker environment with proper initialization
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_CONTAINER_NAME="atma-postgres"
DB_NAME="atma_db"
DB_USER="atma_user"
DB_PASSWORD="atma_secure_password_2024"
POSTGRES_PASSWORD="postgres_admin_password"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}ATMA Backend - Database Setup${NC}"
echo -e "${BLUE}========================================${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    print_status "Checking Docker..."
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    print_status "Docker is running ✓"
}

# Function to check if docker-compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    if ! command -v docker-compose &> /dev/null; then
        print_error "Docker Compose is not installed."
        exit 1
    fi
    print_status "Docker Compose is available ✓"
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p docker/postgres/init-scripts
    mkdir -p data/postgres
    mkdir -p logs/postgres
    mkdir -p backups
    
    # Set proper permissions
    chmod 755 data/postgres
    chmod 755 logs/postgres
    chmod 755 backups
    
    print_status "Directories created ✓"
}

# Function to create PostgreSQL Dockerfile
create_postgres_dockerfile() {
    print_status "Creating PostgreSQL Dockerfile..."
    
    cat > docker/postgres/Dockerfile << 'EOF'
FROM postgres:15-alpine

# Install additional extensions
RUN apk add --no-cache postgresql-contrib

# Copy configuration files
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Set proper permissions
RUN chmod +x /docker-entrypoint-initdb.d/*.sql

# Expose port
EXPOSE 5432

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1
EOF
    
    print_status "PostgreSQL Dockerfile created ✓"
}

# Function to create PostgreSQL configuration
create_postgres_config() {
    print_status "Creating PostgreSQL configuration..."
    
    cat > docker/postgres/postgresql.conf << 'EOF'
# Basic Settings
listen_addresses = '*'
port = 5432
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'all'
log_min_duration_statement = 1000

# Performance
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Locale
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'
EOF

    cat > docker/postgres/pg_hba.conf << 'EOF'
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                trust
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 trust
host    all             all             0.0.0.0/0               md5
EOF
    
    print_status "PostgreSQL configuration created ✓"
}

# Function to create database initialization scripts
create_init_scripts() {
    print_status "Creating database initialization scripts..."
    
    # 01-create-database.sql
    cat > docker/postgres/init-scripts/01-create-database.sql << EOF
-- Create database
CREATE DATABASE ${DB_NAME};

-- Create application user
CREATE USER ${DB_USER} WITH PASSWORD '${DB_PASSWORD}';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE ${DB_NAME} TO ${DB_USER};

-- Connect to the database
\\c ${DB_NAME};

-- Grant schema creation privileges
GRANT CREATE ON DATABASE ${DB_NAME} TO ${DB_USER};
ALTER USER ${DB_USER} CREATEDB;
EOF

    # Copy existing init-database.sql as schema initialization
    if [ -f "init-database.sql" ]; then
        print_status "Copying existing init-database.sql..."
        cp init-database.sql docker/postgres/init-scripts/02-init-schema.sql
        
        # Add user permissions to the schema file
        cat >> docker/postgres/init-scripts/02-init-schema.sql << EOF

-- Grant schema usage to application user
GRANT USAGE ON SCHEMA public TO ${DB_USER};
GRANT USAGE ON SCHEMA auth TO ${DB_USER};
GRANT USAGE ON SCHEMA archive TO ${DB_USER};
GRANT USAGE ON SCHEMA assessment TO ${DB_USER};

-- Grant all privileges on schemas
GRANT ALL ON SCHEMA auth TO ${DB_USER};
GRANT ALL ON SCHEMA archive TO ${DB_USER};
GRANT ALL ON SCHEMA assessment TO ${DB_USER};

-- Grant all privileges on all tables in schemas
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO ${DB_USER};
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO ${DB_USER};
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA archive TO ${DB_USER};
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA assessment TO ${DB_USER};

-- Grant all privileges on all sequences in schemas
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO ${DB_USER};
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO ${DB_USER};
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA archive TO ${DB_USER};
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA assessment TO ${DB_USER};

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON TABLES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON TABLES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON TABLES TO ${DB_USER};

ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON SEQUENCES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON SEQUENCES TO ${DB_USER};
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON SEQUENCES TO ${DB_USER};
EOF
    else
        print_warning "init-database.sql not found. You'll need to create schema manually."
    fi
    
    # 03-init-data.sql
    cat > docker/postgres/init-scripts/03-init-data.sql << EOF
\\c ${DB_NAME};

-- Insert sample schools
INSERT INTO public.schools (name, address, city, province) VALUES
('SMA Negeri 1 Jakarta', 'Jl. Budi Kemuliaan No. 6', 'Jakarta Pusat', 'DKI Jakarta'),
('SMA Negeri 3 Bandung', 'Jl. Belitung No. 8', 'Bandung', 'Jawa Barat'),
('SMA Negeri 1 Surabaya', 'Jl. Wijaya Kusuma No. 48', 'Surabaya', 'Jawa Timur'),
('SMA Negeri 2 Yogyakarta', 'Jl. Bener No. 30', 'Yogyakarta', 'DI Yogyakarta'),
('SMA Negeri 1 Medan', 'Jl. Mistar No. 41', 'Medan', 'Sumatera Utara');

-- Insert default admin user
INSERT INTO auth.users (
    id, 
    username, 
    email, 
    password_hash, 
    user_type, 
    is_active, 
    token_balance
) VALUES (
    uuid_generate_v4(),
    'superadmin',
    '<EMAIL>',
    '\$2b\$10\$8K1p/a0dclxKoNqIfrHb2eUHiNNa6d4fMhEA6bMfLODAOtfSdZWO2', -- admin123
    'superadmin',
    true,
    1000
);

-- Insert admin profile
INSERT INTO auth.user_profiles (
    user_id,
    full_name,
    school_origin,
    gender
) VALUES (
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'System Administrator',
    'ATMA System',
    'male'
);
EOF
    
    print_status "Database initialization scripts created ✓"
}

# Function to create docker-compose configuration for database
create_docker_compose() {
    print_status "Creating docker-compose configuration..."
    
    cat > docker-compose.db.yml << EOF
version: '3.8'

services:
  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    container_name: ${DB_CONTAINER_NAME}
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - atma-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres

networks:
  atma-network:
    driver: bridge
EOF
    
    print_status "Docker Compose configuration created ✓"
}

# Function to start database
start_database() {
    print_status "Starting PostgreSQL database..."
    
    docker-compose -f docker-compose.db.yml up -d postgres
    
    print_status "Waiting for database to be ready..."
    sleep 10
    
    # Wait for database to be healthy
    for i in {1..30}; do
        if docker-compose -f docker-compose.db.yml exec postgres pg_isready -U postgres > /dev/null 2>&1; then
            print_status "Database is ready ✓"
            break
        fi
        echo -n "."
        sleep 2
    done
}

# Function to verify database setup
verify_database() {
    print_status "Verifying database setup..."
    
    # Check if database exists
    if docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw ${DB_NAME}; then
        print_status "Database '${DB_NAME}' exists ✓"
    else
        print_error "Database '${DB_NAME}' not found!"
        return 1
    fi
    
    # Check if user exists
    if docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d ${DB_NAME} -c "SELECT 1 FROM pg_user WHERE usename = '${DB_USER}'" | grep -q "1 row"; then
        print_status "User '${DB_USER}' exists ✓"
    else
        print_error "User '${DB_USER}' not found!"
        return 1
    fi
    
    # Check schemas
    SCHEMAS=$(docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d ${DB_NAME} -c "\dn" | grep -E "(auth|archive|assessment)" | wc -l)
    if [ "$SCHEMAS" -ge 3 ]; then
        print_status "Required schemas exist ✓"
    else
        print_warning "Some schemas might be missing. Check manually."
    fi
    
    # Check sample data
    SCHOOLS_COUNT=$(docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d ${DB_NAME} -c "SELECT COUNT(*) FROM public.schools" | grep -E "^\s*[0-9]+\s*$" | tr -d ' ')
    if [ "$SCHOOLS_COUNT" -gt 0 ]; then
        print_status "Sample schools data exists (${SCHOOLS_COUNT} schools) ✓"
    else
        print_warning "No sample schools data found."
    fi
    
    ADMIN_COUNT=$(docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d ${DB_NAME} -c "SELECT COUNT(*) FROM auth.users WHERE user_type = 'superadmin'" | grep -E "^\s*[0-9]+\s*$" | tr -d ' ')
    if [ "$ADMIN_COUNT" -gt 0 ]; then
        print_status "Admin user exists ✓"
    else
        print_warning "No admin user found."
    fi
}

# Function to show connection info
show_connection_info() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}Database Connection Information${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}Host:${NC} localhost"
    echo -e "${GREEN}Port:${NC} 5432"
    echo -e "${GREEN}Database:${NC} ${DB_NAME}"
    echo -e "${GREEN}Username:${NC} ${DB_USER}"
    echo -e "${GREEN}Password:${NC} ${DB_PASSWORD}"
    echo ""
    echo -e "${GREEN}Admin Connection:${NC}"
    echo -e "${GREEN}Username:${NC} postgres"
    echo -e "${GREEN}Password:${NC} ${POSTGRES_PASSWORD}"
    echo ""
    echo -e "${GREEN}Test Connection:${NC}"
    echo "psql -h localhost -p 5432 -U ${DB_USER} -d ${DB_NAME}"
    echo ""
    echo -e "${GREEN}Docker Commands:${NC}"
    echo "docker-compose -f docker-compose.db.yml logs -f postgres"
    echo "docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d ${DB_NAME}"
    echo -e "${BLUE}========================================${NC}"
}

# Main execution
main() {
    check_docker
    check_docker_compose
    create_directories
    create_postgres_dockerfile
    create_postgres_config
    create_init_scripts
    create_docker_compose
    start_database
    verify_database
    show_connection_info
    
    print_status "Database setup completed successfully! 🎉"
}

# Run main function
main "$@"
