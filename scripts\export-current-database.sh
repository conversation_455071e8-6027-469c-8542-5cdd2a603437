#!/bin/bash

# =====================================================
# ATMA Backend - Export Current Database Script
# =====================================================
# This script exports the current database schema and data
# for use in Docker environment
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration - Update these based on your current database
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="atma_db"
DB_USER="atma_user"  # or postgres
DB_PASSWORD=""  # will prompt if not set

# Output directory
OUTPUT_DIR="database_exports"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to check if database is accessible
check_database_connection() {
    print_status "Checking database connection..."
    
    if pg_isready -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME > /dev/null 2>&1; then
        print_status "Database connection successful ✓"
    else
        print_error "Cannot connect to database!"
        print_status "Please check your database connection settings:"
        echo "Host: $DB_HOST"
        echo "Port: $DB_PORT"
        echo "Database: $DB_NAME"
        echo "User: $DB_USER"
        exit 1
    fi
}

# Function to create output directory
create_output_directory() {
    print_status "Creating output directory..."
    
    mkdir -p $OUTPUT_DIR
    print_status "Output directory created: $OUTPUT_DIR ✓"
}

# Function to export complete database
export_complete_database() {
    print_status "Exporting complete database (schema + data)..."
    
    local output_file="$OUTPUT_DIR/complete_database_${TIMESTAMP}.sql"
    
    pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME > $output_file
    
    if [ $? -eq 0 ]; then
        print_status "Complete database exported: $output_file ✓"
        
        # Get file size
        local file_size=$(du -h $output_file | cut -f1)
        print_status "Export size: $file_size"
    else
        print_error "Failed to export complete database!"
        return 1
    fi
}

# Function to export schema only
export_schema_only() {
    print_status "Exporting database schema only..."
    
    local output_file="$OUTPUT_DIR/schema_only_${TIMESTAMP}.sql"
    
    pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema-only > $output_file
    
    if [ $? -eq 0 ]; then
        print_status "Schema exported: $output_file ✓"
    else
        print_error "Failed to export schema!"
        return 1
    fi
}

# Function to export data only
export_data_only() {
    print_status "Exporting database data only..."
    
    local output_file="$OUTPUT_DIR/data_only_${TIMESTAMP}.sql"
    
    pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --data-only > $output_file
    
    if [ $? -eq 0 ]; then
        print_status "Data exported: $output_file ✓"
    else
        print_error "Failed to export data!"
        return 1
    fi
}

# Function to export by schema
export_by_schema() {
    print_status "Exporting individual schemas..."
    
    local schemas=("public" "auth" "archive" "assessment")
    
    for schema in "${schemas[@]}"; do
        print_status "Exporting schema: $schema"
        
        local output_file="$OUTPUT_DIR/schema_${schema}_${TIMESTAMP}.sql"
        
        # Check if schema exists
        local schema_exists=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT 1 FROM information_schema.schemata WHERE schema_name = '$schema';" | xargs)
        
        if [ "$schema_exists" = "1" ]; then
            pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME --schema=$schema > $output_file
            
            if [ $? -eq 0 ]; then
                print_status "Schema $schema exported: $output_file ✓"
            else
                print_warning "Failed to export schema: $schema"
            fi
        else
            print_warning "Schema $schema does not exist, skipping..."
        fi
    done
}

# Function to create Docker-compatible init scripts
create_docker_init_scripts() {
    print_status "Creating Docker-compatible initialization scripts..."
    
    local docker_init_dir="docker/postgres/init-scripts"
    mkdir -p $docker_init_dir
    
    # 1. Create database and user script
    cat > $docker_init_dir/01-create-database.sql << 'EOF'
-- Create database
CREATE DATABASE atma_db;

-- Create application user
CREATE USER atma_user WITH PASSWORD 'atma_secure_password_2024';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;

-- Connect to the database
\c atma_db;

-- Grant schema creation privileges
GRANT CREATE ON DATABASE atma_db TO atma_user;
ALTER USER atma_user CREATEDB;
EOF

    # 2. Copy the latest complete database dump
    local latest_dump=$(ls -t $OUTPUT_DIR/complete_database_*.sql | head -1)
    if [ -f "$latest_dump" ]; then
        cp "$latest_dump" "$docker_init_dir/02-restore-database.sql"
        print_status "Latest database dump copied to Docker init scripts ✓"
        
        # Add user permissions to the end of the file
        cat >> $docker_init_dir/02-restore-database.sql << 'EOF'

-- Grant schema usage to application user
GRANT USAGE ON SCHEMA public TO atma_user;
GRANT USAGE ON SCHEMA auth TO atma_user;
GRANT USAGE ON SCHEMA archive TO atma_user;
GRANT USAGE ON SCHEMA assessment TO atma_user;

-- Grant all privileges on schemas
GRANT ALL ON SCHEMA auth TO atma_user;
GRANT ALL ON SCHEMA archive TO atma_user;
GRANT ALL ON SCHEMA assessment TO atma_user;

-- Grant all privileges on all tables in schemas
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA assessment TO atma_user;

-- Grant all privileges on all sequences in schemas
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA assessment TO atma_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON TABLES TO atma_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON SEQUENCES TO atma_user;
EOF
        
        print_status "Docker initialization scripts created ✓"
    else
        print_error "No database dump found to copy!"
        return 1
    fi
}

# Function to show database info
show_database_info() {
    print_status "Gathering database information..."
    
    echo -e "${BLUE}Database Information:${NC}"
    echo "Host: $DB_HOST"
    echo "Port: $DB_PORT"
    echo "Database: $DB_NAME"
    echo "User: $DB_USER"
    echo ""
    
    # Get database size
    local db_size=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT pg_size_pretty(pg_database_size('$DB_NAME'));" | xargs)
    echo -e "${GREEN}Database Size:${NC} $db_size"
    
    # Get schema info
    echo -e "${GREEN}Schemas:${NC}"
    psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "\dn"
    
    # Get table counts per schema
    echo -e "${GREEN}Table Counts:${NC}"
    local schemas=("public" "auth" "archive" "assessment")
    for schema in "${schemas[@]}"; do
        local table_count=$(psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = '$schema';" | xargs)
        if [ "$table_count" -gt 0 ]; then
            echo "  $schema: $table_count tables"
        fi
    done
}

# Function to show export summary
show_export_summary() {
    print_header "Export Summary"
    
    echo -e "${GREEN}Exported Files:${NC}"
    if [ -d "$OUTPUT_DIR" ]; then
        ls -la $OUTPUT_DIR/
    fi
    
    echo ""
    echo -e "${GREEN}Docker Files Created:${NC}"
    if [ -d "docker/postgres/init-scripts" ]; then
        ls -la docker/postgres/init-scripts/
    fi
    
    echo ""
    echo -e "${GREEN}Next Steps:${NC}"
    echo "1. Review the exported files in $OUTPUT_DIR/"
    echo "2. Docker init scripts are ready in docker/postgres/init-scripts/"
    echo "3. Run: ./scripts/setup-docker.sh"
    echo "4. Or start database: docker-compose up -d postgres"
}

# Function to show help
show_help() {
    echo "ATMA Database Export Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -i, --info          Show database information only"
    echo "  --host HOST         Database host (default: localhost)"
    echo "  --port PORT         Database port (default: 5432)"
    echo "  --user USER         Database user (default: atma_user)"
    echo "  --database DB       Database name (default: atma_db)"
    echo "  --schema-only       Export schema only"
    echo "  --data-only         Export data only"
    echo ""
    echo "Examples:"
    echo "  $0                  Export complete database"
    echo "  $0 --info           Show database information"
    echo "  $0 --schema-only    Export schema only"
    echo "  $0 --host ************* --user postgres  Export from remote host"
}

# Parse command line arguments
EXPORT_TYPE="complete"
INFO_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -i|--info)
            INFO_ONLY=true
            shift
            ;;
        --host)
            DB_HOST="$2"
            shift 2
            ;;
        --port)
            DB_PORT="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --schema-only)
            EXPORT_TYPE="schema"
            shift
            ;;
        --data-only)
            EXPORT_TYPE="data"
            shift
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_header "ATMA Database Export"
    
    check_database_connection
    show_database_info
    
    if [ "$INFO_ONLY" = true ]; then
        exit 0
    fi
    
    create_output_directory
    
    case $EXPORT_TYPE in
        "complete")
            export_complete_database
            export_schema_only
            export_data_only
            export_by_schema
            create_docker_init_scripts
            ;;
        "schema")
            export_schema_only
            export_by_schema
            ;;
        "data")
            export_data_only
            ;;
    esac
    
    show_export_summary
    
    print_status "Database export completed successfully! 🎉"
}

# Run main function
main "$@"
