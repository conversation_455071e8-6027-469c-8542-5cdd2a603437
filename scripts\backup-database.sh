#!/bin/bash

# =====================================================
# ATMA Backend - Database Backup Script
# =====================================================
# This script creates backups of PostgreSQL database
# and manages backup retention
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_CONTAINER_NAME="atma-postgres"
DB_NAME="atma_db"
DB_USER="postgres"
BACKUP_DIR="./backups"
RETENTION_DAYS=7

# Get current date and time
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="atma_db_backup_${DATE}.sql"
COMPRESSED_FILE="${BACKUP_FILE}.gz"

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if container is running
check_container() {
    print_status "Checking if database container is running..."
    
    if ! docker ps | grep -q ${DB_CONTAINER_NAME}; then
        print_error "Database container '${DB_CONTAINER_NAME}' is not running!"
        print_status "Start it with: docker-compose -f docker-compose.db.yml up -d postgres"
        exit 1
    fi
    
    print_status "Database container is running ✓"
}

# Function to create backup directory
create_backup_dir() {
    if [ ! -d "${BACKUP_DIR}" ]; then
        print_status "Creating backup directory..."
        mkdir -p ${BACKUP_DIR}
    fi
    
    print_status "Backup directory ready ✓"
}

# Function to create database backup
create_backup() {
    print_status "Creating database backup..."
    print_status "Backup file: ${BACKUP_DIR}/${COMPRESSED_FILE}"
    
    # Create backup using pg_dump
    docker exec ${DB_CONTAINER_NAME} pg_dump -U ${DB_USER} ${DB_NAME} > ${BACKUP_DIR}/${BACKUP_FILE}
    
    if [ $? -eq 0 ]; then
        print_status "Database dump created successfully ✓"
        
        # Compress the backup
        print_status "Compressing backup..."
        gzip ${BACKUP_DIR}/${BACKUP_FILE}
        
        if [ $? -eq 0 ]; then
            print_status "Backup compressed successfully ✓"
            
            # Get file size
            BACKUP_SIZE=$(du -h ${BACKUP_DIR}/${COMPRESSED_FILE} | cut -f1)
            print_status "Backup size: ${BACKUP_SIZE}"
        else
            print_error "Failed to compress backup!"
            exit 1
        fi
    else
        print_error "Failed to create database backup!"
        exit 1
    fi
}

# Function to clean old backups
cleanup_old_backups() {
    print_status "Cleaning up old backups (older than ${RETENTION_DAYS} days)..."
    
    # Find and delete old backup files
    OLD_BACKUPS=$(find ${BACKUP_DIR} -name "atma_db_backup_*.sql.gz" -mtime +${RETENTION_DAYS} 2>/dev/null)
    
    if [ -n "$OLD_BACKUPS" ]; then
        echo "$OLD_BACKUPS" | while read -r file; do
            print_status "Removing old backup: $(basename $file)"
            rm -f "$file"
        done
        print_status "Old backups cleaned up ✓"
    else
        print_status "No old backups to clean up ✓"
    fi
}

# Function to list existing backups
list_backups() {
    print_status "Existing backups:"
    echo -e "${BLUE}========================================${NC}"
    
    if ls ${BACKUP_DIR}/atma_db_backup_*.sql.gz 1> /dev/null 2>&1; then
        for backup in ${BACKUP_DIR}/atma_db_backup_*.sql.gz; do
            FILENAME=$(basename $backup)
            FILESIZE=$(du -h $backup | cut -f1)
            FILEDATE=$(stat -c %y $backup | cut -d' ' -f1,2 | cut -d'.' -f1)
            echo -e "${GREEN}${FILENAME}${NC} - ${FILESIZE} - ${FILEDATE}"
        done
    else
        echo -e "${YELLOW}No backups found${NC}"
    fi
    
    echo -e "${BLUE}========================================${NC}"
}

# Function to verify backup
verify_backup() {
    print_status "Verifying backup integrity..."
    
    # Test if the compressed file can be read
    if gzip -t ${BACKUP_DIR}/${COMPRESSED_FILE}; then
        print_status "Backup file integrity verified ✓"
        
        # Check if backup contains data
        BACKUP_SIZE_BYTES=$(stat -c%s ${BACKUP_DIR}/${COMPRESSED_FILE})
        if [ $BACKUP_SIZE_BYTES -gt 1000 ]; then
            print_status "Backup appears to contain data ✓"
        else
            print_warning "Backup file seems too small (${BACKUP_SIZE_BYTES} bytes)"
        fi
    else
        print_error "Backup file is corrupted!"
        exit 1
    fi
}

# Function to show backup summary
show_summary() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}Backup Summary${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}Database:${NC} ${DB_NAME}"
    echo -e "${GREEN}Backup File:${NC} ${COMPRESSED_FILE}"
    echo -e "${GREEN}Location:${NC} ${BACKUP_DIR}/${COMPRESSED_FILE}"
    echo -e "${GREEN}Size:${NC} $(du -h ${BACKUP_DIR}/${COMPRESSED_FILE} | cut -f1)"
    echo -e "${GREEN}Created:${NC} $(date)"
    echo ""
    echo -e "${GREEN}Restore Command:${NC}"
    echo "gunzip -c ${BACKUP_DIR}/${COMPRESSED_FILE} | docker exec -i ${DB_CONTAINER_NAME} psql -U ${DB_USER} ${DB_NAME}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to show help
show_help() {
    echo "ATMA Database Backup Script"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -l, --list          List existing backups"
    echo "  -c, --cleanup       Only cleanup old backups"
    echo "  -r, --retention N   Set retention days (default: 7)"
    echo ""
    echo "Examples:"
    echo "  $0                  Create a new backup"
    echo "  $0 --list           List all existing backups"
    echo "  $0 --cleanup        Clean up old backups only"
    echo "  $0 --retention 14   Create backup with 14 days retention"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--list)
            list_backups
            exit 0
            ;;
        -c|--cleanup)
            cleanup_old_backups
            exit 0
            ;;
        -r|--retention)
            RETENTION_DAYS="$2"
            shift 2
            ;;
        *)
            print_error "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}ATMA Backend - Database Backup${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    check_container
    create_backup_dir
    create_backup
    verify_backup
    cleanup_old_backups
    show_summary
    
    print_status "Backup completed successfully! 🎉"
}

# Run main function
main "$@"
