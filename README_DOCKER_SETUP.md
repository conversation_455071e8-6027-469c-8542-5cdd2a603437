# 🐳 ATMA Backend - Docker Setup Guide

## 📋 Overview

Panduan lengkap untuk setup ATMA Backend menggunakan Docker dan Docker Compose. Sistem ini terdiri dari 6 microservices dengan PostgreSQL database dan RabbitMQ message queue.

## 🏗️ Arsitektur

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Compose Stack                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ API Gateway │  │ Auth Service│  │Archive Svc  │         │
│  │   :3000     │  │    :3001    │  │    :3002    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Assessment   │  │Analysis     │  │Notification │         │
│  │Svc :3003    │  │Worker       │  │Svc :3005    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ PostgreSQL  │  │  RabbitMQ   │  │   Redis     │         │
│  │   :5432     │  │   :5672     │  │   :6379     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start (Otomatis)

### 1. Persiapan
```bash
# Pastikan Docker dan Docker Compose terinstall
docker --version
docker-compose --version

# Clone atau masuk ke direktori project
cd atma-backend

# Buat semua file Docker yang diperlukan
chmod +x scripts/create-docker-files.sh
./scripts/create-docker-files.sh
```

### 2. Setup Lengkap
```bash
# Setup lengkap dengan satu command
chmod +x scripts/setup-docker.sh
./scripts/setup-docker.sh
```

### 3. Verifikasi
```bash
# Check status containers
docker-compose ps

# Test API
curl http://localhost:3000/health
```

## 🔧 Setup Manual (Step by Step)

### Step 1: Persiapan Environment

```bash
# Copy environment template
cp .env.docker .env

# Edit .env dan update nilai-nilai berikut:
# - GOOGLE_AI_API_KEY (dari Google AI Studio)
# - Database passwords
# - JWT secrets
```

### Step 2: Setup Database

```bash
# Setup database PostgreSQL
chmod +x scripts/setup-database.sh
./scripts/setup-database.sh

# Atau manual
docker-compose -f docker-compose.db.yml up -d postgres
```

### Step 3: Build dan Start Services

```bash
# Build semua images
docker-compose build

# Start infrastructure services
docker-compose up -d postgres rabbitmq redis

# Wait for services to be ready
sleep 30

# Start application services
docker-compose up -d
```

## 📊 Database Setup Detail

### Otomatis dengan Script
```bash
./scripts/setup-database.sh
```

### Manual Setup
```bash
# 1. Buat direktori
mkdir -p docker/postgres/init-scripts
mkdir -p data/postgres
mkdir -p logs/postgres
mkdir -p backups

# 2. Copy init-database.sql
cp init-database.sql docker/postgres/init-scripts/02-init-schema.sql

# 3. Start database
docker-compose -f docker-compose.db.yml up -d postgres

# 4. Verify
docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d atma_db -c "\dt auth.*"
```

### Database Connection Info
```
Host: localhost
Port: 5432
Database: atma_db
Username: atma_user
Password: atma_secure_password_2024

Admin Connection:
Username: postgres
Password: postgres_admin_password
```

### Query untuk Verifikasi Database

```sql
-- Check schemas
\dn

-- Check tables
\dt public.*
\dt auth.*
\dt archive.*
\dt assessment.*

-- Check sample data
SELECT * FROM public.schools LIMIT 5;
SELECT username, email, user_type FROM auth.users;

-- Check admin user
SELECT * FROM auth.users WHERE user_type = 'superadmin';
```

## 🛠️ Management Commands

### Container Management
```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# Restart specific service
docker-compose restart auth-service

# Scale analysis worker
docker-compose up -d --scale analysis-worker=3

# View logs
docker-compose logs -f api-gateway
docker-compose logs -f postgres
```

### Database Management
```bash
# Backup database
./scripts/backup-database.sh

# List backups
./scripts/backup-database.sh --list

# Restore database
./scripts/restore-database.sh

# Connect to database
docker-compose exec postgres psql -U postgres -d atma_db
```

### Health Checks
```bash
# Check all services
docker-compose ps

# API Gateway health
curl http://localhost:3000/health

# Individual service health
curl http://localhost:3001/health  # Auth Service
curl http://localhost:3002/health  # Archive Service
curl http://localhost:3003/health  # Assessment Service
curl http://localhost:3005/health  # Notification Service
```

## 🌐 Service URLs

| Service | URL | Description |
|---------|-----|-------------|
| API Gateway | http://localhost:3000 | Main entry point |
| Auth Service | http://localhost:3001 | Authentication |
| Archive Service | http://localhost:3002 | Analysis results |
| Assessment Service | http://localhost:3003 | Assessment data |
| Notification Service | http://localhost:3005 | WebSocket notifications |
| RabbitMQ Management | http://localhost:15672 | Message queue UI |

### Default Credentials
- **RabbitMQ**: atma_user / atma_rabbitmq_password
- **Database**: atma_user / atma_secure_password_2024
- **Admin User**: <EMAIL> / admin123

## 🔍 Troubleshooting

### Common Issues

#### 1. Database Connection Failed
```bash
# Check if postgres container is running
docker-compose ps postgres

# Check postgres logs
docker-compose logs postgres

# Restart postgres
docker-compose restart postgres
```

#### 2. Port Already in Use
```bash
# Check what's using the port
netstat -tulpn | grep :5432

# Stop conflicting service or change port in docker-compose.yml
```

#### 3. Permission Denied
```bash
# Fix permissions
sudo chown -R $USER:$USER data/
chmod 755 data/postgres
```

#### 4. Out of Disk Space
```bash
# Clean up Docker
docker system prune -a

# Remove unused volumes
docker volume prune
```

### Debug Commands
```bash
# Check container status
docker-compose ps

# View all logs
docker-compose logs

# Execute command in container
docker-compose exec postgres bash
docker-compose exec api-gateway sh

# Check network connectivity
docker-compose exec api-gateway ping postgres
```

## 📈 Performance Tuning

### Resource Limits
Edit `docker-compose.yml` untuk adjust resource limits:

```yaml
services:
  postgres:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
```

### Scaling Services
```bash
# Scale analysis workers
docker-compose up -d --scale analysis-worker=3

# Scale with resource limits
docker-compose up -d --scale analysis-worker=3 --scale auth-service=2
```

## 🔒 Security Considerations

### Production Setup
1. **Change all default passwords**
2. **Use strong JWT secrets**
3. **Don't expose database ports**
4. **Use SSL/TLS certificates**
5. **Regular security updates**

### Environment Variables
```bash
# Production .env example
NODE_ENV=production
JWT_SECRET=your_very_strong_jwt_secret_here
DB_PASSWORD=your_strong_database_password
RABBITMQ_PASSWORD=your_strong_rabbitmq_password
```

## 📋 File Structure

```
atma-backend/
├── docker-compose.yml              # Main orchestration
├── .env                           # Environment variables
├── docker/
│   └── postgres/                  # PostgreSQL configuration
├── scripts/
│   ├── setup-docker.sh           # Complete setup
│   ├── setup-database.sh         # Database setup
│   ├── backup-database.sh        # Database backup
│   └── restore-database.sh       # Database restore
├── data/                          # Persistent data
├── logs/                          # Application logs
└── backups/                       # Database backups
```

## 🆘 Support

Jika mengalami masalah:

1. **Check logs**: `docker-compose logs [service-name]`
2. **Verify environment**: Pastikan `.env` sudah benar
3. **Check resources**: Pastikan disk space dan memory cukup
4. **Restart services**: `docker-compose restart`
5. **Clean rebuild**: `docker-compose down && docker-compose build --no-cache && docker-compose up -d`

---

**Status**: ✅ Ready to Use
**Last Updated**: 2024
**Version**: 1.0
