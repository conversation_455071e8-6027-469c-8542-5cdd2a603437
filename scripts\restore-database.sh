#!/bin/bash

# =====================================================
# ATMA Backend - Database Restore Script
# =====================================================
# This script restores PostgreSQL database from backup
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DB_CONTAINER_NAME="atma-postgres"
DB_NAME="atma_db"
DB_USER="postgres"
BACKUP_DIR="./backups"

# Variables
BACKUP_FILE=""
FORCE_RESTORE=false

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if container is running
check_container() {
    print_status "Checking if database container is running..."
    
    if ! docker ps | grep -q ${DB_CONTAINER_NAME}; then
        print_error "Database container '${DB_CONTAINER_NAME}' is not running!"
        print_status "Start it with: docker-compose -f docker-compose.db.yml up -d postgres"
        exit 1
    fi
    
    print_status "Database container is running ✓"
}

# Function to list available backups
list_backups() {
    echo -e "${BLUE}Available backups:${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    if ls ${BACKUP_DIR}/atma_db_backup_*.sql.gz 1> /dev/null 2>&1; then
        local counter=1
        for backup in ${BACKUP_DIR}/atma_db_backup_*.sql.gz; do
            FILENAME=$(basename $backup)
            FILESIZE=$(du -h $backup | cut -f1)
            FILEDATE=$(stat -c %y $backup | cut -d' ' -f1,2 | cut -d'.' -f1)
            echo -e "${GREEN}${counter}.${NC} ${FILENAME} - ${FILESIZE} - ${FILEDATE}"
            ((counter++))
        done
    else
        echo -e "${YELLOW}No backups found in ${BACKUP_DIR}${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}========================================${NC}"
}

# Function to select backup file
select_backup() {
    if [ -z "$BACKUP_FILE" ]; then
        list_backups
        echo ""
        read -p "Enter backup filename (or number from list): " selection
        
        # Check if selection is a number
        if [[ $selection =~ ^[0-9]+$ ]]; then
            # Get backup by number
            local counter=1
            for backup in ${BACKUP_DIR}/atma_db_backup_*.sql.gz; do
                if [ $counter -eq $selection ]; then
                    BACKUP_FILE=$(basename $backup)
                    break
                fi
                ((counter++))
            done
        else
            # Use selection as filename
            BACKUP_FILE=$selection
        fi
    fi
    
    # Ensure backup file has full path
    if [[ ! "$BACKUP_FILE" =~ ^/ ]]; then
        BACKUP_FILE="${BACKUP_DIR}/${BACKUP_FILE}"
    fi
    
    # Check if backup file exists
    if [ ! -f "$BACKUP_FILE" ]; then
        print_error "Backup file not found: $BACKUP_FILE"
        exit 1
    fi
    
    print_status "Selected backup: $(basename $BACKUP_FILE)"
}

# Function to verify backup file
verify_backup() {
    print_status "Verifying backup file..."
    
    # Test if the compressed file can be read
    if gzip -t "$BACKUP_FILE"; then
        print_status "Backup file integrity verified ✓"
        
        # Check if backup contains data
        BACKUP_SIZE_BYTES=$(stat -c%s "$BACKUP_FILE")
        if [ $BACKUP_SIZE_BYTES -gt 1000 ]; then
            print_status "Backup appears to contain data ✓"
        else
            print_warning "Backup file seems too small (${BACKUP_SIZE_BYTES} bytes)"
            if [ "$FORCE_RESTORE" = false ]; then
                read -p "Continue anyway? (y/N): " confirm
                if [[ ! $confirm =~ ^[Yy]$ ]]; then
                    print_status "Restore cancelled."
                    exit 0
                fi
            fi
        fi
    else
        print_error "Backup file is corrupted!"
        exit 1
    fi
}

# Function to confirm restore
confirm_restore() {
    if [ "$FORCE_RESTORE" = false ]; then
        echo -e "${YELLOW}========================================${NC}"
        echo -e "${YELLOW}WARNING: This will REPLACE all data in the database!${NC}"
        echo -e "${YELLOW}========================================${NC}"
        echo -e "${RED}Database:${NC} ${DB_NAME}"
        echo -e "${RED}Backup:${NC} $(basename $BACKUP_FILE)"
        echo -e "${RED}Size:${NC} $(du -h $BACKUP_FILE | cut -f1)"
        echo ""
        read -p "Are you sure you want to continue? (y/N): " confirm
        
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            print_status "Restore cancelled."
            exit 0
        fi
    fi
}

# Function to create pre-restore backup
create_pre_restore_backup() {
    print_status "Creating pre-restore backup..."
    
    local pre_restore_date=$(date +%Y%m%d_%H%M%S)
    local pre_restore_file="${BACKUP_DIR}/pre_restore_backup_${pre_restore_date}.sql"
    
    docker exec ${DB_CONTAINER_NAME} pg_dump -U ${DB_USER} ${DB_NAME} > ${pre_restore_file}
    
    if [ $? -eq 0 ]; then
        gzip ${pre_restore_file}
        print_status "Pre-restore backup created: pre_restore_backup_${pre_restore_date}.sql.gz ✓"
    else
        print_warning "Failed to create pre-restore backup, but continuing..."
    fi
}

# Function to drop and recreate database
recreate_database() {
    print_status "Recreating database..."
    
    # Drop existing database
    docker exec ${DB_CONTAINER_NAME} psql -U ${DB_USER} -c "DROP DATABASE IF EXISTS ${DB_NAME};"
    
    if [ $? -eq 0 ]; then
        print_status "Existing database dropped ✓"
    else
        print_error "Failed to drop existing database!"
        exit 1
    fi
    
    # Create new database
    docker exec ${DB_CONTAINER_NAME} psql -U ${DB_USER} -c "CREATE DATABASE ${DB_NAME};"
    
    if [ $? -eq 0 ]; then
        print_status "New database created ✓"
    else
        print_error "Failed to create new database!"
        exit 1
    fi
}

# Function to restore database
restore_database() {
    print_status "Restoring database from backup..."
    
    # Restore database from backup
    gunzip -c "$BACKUP_FILE" | docker exec -i ${DB_CONTAINER_NAME} psql -U ${DB_USER} ${DB_NAME}
    
    if [ $? -eq 0 ]; then
        print_status "Database restored successfully ✓"
    else
        print_error "Failed to restore database!"
        exit 1
    fi
}

# Function to verify restore
verify_restore() {
    print_status "Verifying restore..."
    
    # Check if database exists and has tables
    local table_count=$(docker exec ${DB_CONTAINER_NAME} psql -U ${DB_USER} -d ${DB_NAME} -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema NOT IN ('information_schema', 'pg_catalog');" | grep -E "^\s*[0-9]+\s*$" | tr -d ' ')
    
    if [ "$table_count" -gt 0 ]; then
        print_status "Database contains ${table_count} tables ✓"
        
        # Check specific schemas
        local auth_tables=$(docker exec ${DB_CONTAINER_NAME} psql -U ${DB_USER} -d ${DB_NAME} -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'auth';" | grep -E "^\s*[0-9]+\s*$" | tr -d ' ')
        local archive_tables=$(docker exec ${DB_CONTAINER_NAME} psql -U ${DB_USER} -d ${DB_NAME} -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'archive';" | grep -E "^\s*[0-9]+\s*$" | tr -d ' ')
        
        print_status "Auth schema tables: ${auth_tables}"
        print_status "Archive schema tables: ${archive_tables}"
        
        # Check for admin user
        local admin_count=$(docker exec ${DB_CONTAINER_NAME} psql -U ${DB_USER} -d ${DB_NAME} -c "SELECT COUNT(*) FROM auth.users WHERE user_type = 'superadmin';" 2>/dev/null | grep -E "^\s*[0-9]+\s*$" | tr -d ' ')
        if [ "$admin_count" -gt 0 ]; then
            print_status "Admin users found: ${admin_count} ✓"
        else
            print_warning "No admin users found in restored database"
        fi
    else
        print_warning "Database appears to be empty after restore"
    fi
}

# Function to show restore summary
show_summary() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}Restore Summary${NC}"
    echo -e "${BLUE}========================================${NC}"
    echo -e "${GREEN}Database:${NC} ${DB_NAME}"
    echo -e "${GREEN}Restored from:${NC} $(basename $BACKUP_FILE)"
    echo -e "${GREEN}Backup size:${NC} $(du -h $BACKUP_FILE | cut -f1)"
    echo -e "${GREEN}Restored at:${NC} $(date)"
    echo ""
    echo -e "${GREEN}Connection Info:${NC}"
    echo "Host: localhost"
    echo "Port: 5432"
    echo "Database: ${DB_NAME}"
    echo "Username: atma_user"
    echo -e "${BLUE}========================================${NC}"
}

# Function to show help
show_help() {
    echo "ATMA Database Restore Script"
    echo ""
    echo "Usage: $0 [OPTIONS] [BACKUP_FILE]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Show this help message"
    echo "  -l, --list          List available backups"
    echo "  -f, --force         Force restore without confirmation"
    echo "  -b, --backup FILE   Specify backup file to restore"
    echo ""
    echo "Examples:"
    echo "  $0                                    Interactive restore"
    echo "  $0 --list                             List available backups"
    echo "  $0 --backup backup_file.sql.gz        Restore specific backup"
    echo "  $0 --force --backup backup_file.sql.gz Force restore without confirmation"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -l|--list)
            list_backups
            exit 0
            ;;
        -f|--force)
            FORCE_RESTORE=true
            shift
            ;;
        -b|--backup)
            BACKUP_FILE="$2"
            shift 2
            ;;
        *)
            if [ -z "$BACKUP_FILE" ]; then
                BACKUP_FILE="$1"
            else
                print_error "Unknown option: $1"
                show_help
                exit 1
            fi
            shift
            ;;
    esac
done

# Main execution
main() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}ATMA Backend - Database Restore${NC}"
    echo -e "${BLUE}========================================${NC}"
    
    check_container
    select_backup
    verify_backup
    confirm_restore
    create_pre_restore_backup
    recreate_database
    restore_database
    verify_restore
    show_summary
    
    print_status "Database restore completed successfully! 🎉"
}

# Run main function
main "$@"
