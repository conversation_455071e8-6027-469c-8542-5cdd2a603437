#!/bin/bash

# =====================================================
# ATMA Backend - Update Docker Database Script
# =====================================================
# This script updates Docker setup to use current database
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to backup old init-database.sql
backup_old_init_file() {
    if [ -f "init-database.sql" ]; then
        print_status "Backing up old init-database.sql..."
        cp init-database.sql "init-database.sql.backup.$(date +%Y%m%d_%H%M%S)"
        print_status "Old file backed up ✓"
    fi
}

# Function to check if export exists
check_export_exists() {
    if [ ! -d "database_exports" ]; then
        print_error "No database exports found!"
        print_status "Please run: ./scripts/export-current-database.sh first"
        exit 1
    fi
    
    # Find latest complete database export
    local latest_export=$(ls -t database_exports/complete_database_*.sql 2>/dev/null | head -1)
    if [ -z "$latest_export" ]; then
        print_error "No complete database export found!"
        print_status "Please run: ./scripts/export-current-database.sh first"
        exit 1
    fi
    
    print_status "Found latest export: $(basename $latest_export) ✓"
    echo "$latest_export"
}

# Function to update Docker init scripts
update_docker_init_scripts() {
    local export_file="$1"
    
    print_status "Updating Docker initialization scripts..."
    
    # Create directory
    mkdir -p docker/postgres/init-scripts
    
    # Remove old scripts
    rm -f docker/postgres/init-scripts/*.sql
    
    # 1. Create database and user script
    cat > docker/postgres/init-scripts/01-create-database.sql << 'EOF'
-- Create database
CREATE DATABASE atma_db;

-- Create application user
CREATE USER atma_user WITH PASSWORD 'atma_secure_password_2024';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;

-- Connect to the database
\c atma_db;

-- Grant schema creation privileges
GRANT CREATE ON DATABASE atma_db TO atma_user;
ALTER USER atma_user CREATEDB;
EOF

    # 2. Copy current database dump
    cp "$export_file" docker/postgres/init-scripts/02-restore-current-database.sql
    
    # 3. Add permissions script
    cat > docker/postgres/init-scripts/03-set-permissions.sql << 'EOF'
\c atma_db;

-- Grant schema usage to application user
DO $$
DECLARE
    schema_name text;
BEGIN
    FOR schema_name IN SELECT nspname FROM pg_namespace WHERE nspname NOT IN ('information_schema', 'pg_catalog', 'pg_toast') LOOP
        EXECUTE 'GRANT USAGE ON SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'GRANT ALL ON SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA ' || quote_ident(schema_name) || ' TO atma_user';
        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name) || ' GRANT ALL ON TABLES TO atma_user';
        EXECUTE 'ALTER DEFAULT PRIVILEGES IN SCHEMA ' || quote_ident(schema_name) || ' GRANT ALL ON SEQUENCES TO atma_user';
    END LOOP;
END $$;
EOF

    print_status "Docker initialization scripts updated ✓"
}

# Function to update main init-database.sql
update_main_init_file() {
    local export_file="$1"
    
    print_status "Updating main init-database.sql file..."
    
    # Copy the export as new init file
    cp "$export_file" init-database.sql
    
    print_status "Main init-database.sql updated ✓"
}

# Function to create database comparison
create_comparison_report() {
    print_status "Creating database comparison report..."
    
    local report_file="database_comparison_$(date +%Y%m%d_%H%M%S).md"
    
    cat > "$report_file" << EOF
# Database Update Report

## Update Information
- **Date**: $(date)
- **Old init-database.sql**: Backed up with timestamp
- **New source**: Current running database

## Changes Made
1. Exported current database schema and data
2. Updated Docker initialization scripts
3. Replaced init-database.sql with current database dump

## Files Updated
- \`init-database.sql\` - Main initialization file
- \`docker/postgres/init-scripts/\` - Docker initialization scripts

## Database Export Details
EOF

    # Add export file info
    if [ -d "database_exports" ]; then
        echo "### Exported Files" >> "$report_file"
        ls -la database_exports/ >> "$report_file"
    fi
    
    # Add schema information if available
    if [ -f "database_exports/schema_only_"*.sql ]; then
        echo "" >> "$report_file"
        echo "### Schema Information" >> "$report_file"
        echo "Schema export available for detailed comparison" >> "$report_file"
    fi
    
    cat >> "$report_file" << EOF

## Next Steps
1. Test Docker setup: \`./scripts/setup-docker.sh\`
2. Verify database: \`docker-compose exec postgres psql -U postgres -d atma_db\`
3. Check application connectivity

## Rollback Instructions
If needed, restore from backup:
\`\`\`bash
# Find backup file
ls init-database.sql.backup.*

# Restore specific backup
cp init-database.sql.backup.YYYYMMDD_HHMMSS init-database.sql
\`\`\`
EOF

    print_status "Comparison report created: $report_file ✓"
}

# Function to verify Docker setup
verify_docker_setup() {
    print_status "Verifying Docker setup files..."
    
    # Check if docker-compose.yml exists
    if [ ! -f "docker-compose.yml" ]; then
        print_warning "docker-compose.yml not found. Creating basic version..."
        
        # Create basic docker-compose for database
        cat > docker-compose.db.yml << 'EOF'
version: '3.8'

services:
  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    container_name: atma-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_admin_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - atma-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

volumes:
  postgres_data:
    driver: local

networks:
  atma-network:
    driver: bridge
EOF
    fi
    
    # Check PostgreSQL Dockerfile
    if [ ! -f "docker/postgres/Dockerfile" ]; then
        print_status "Creating PostgreSQL Dockerfile..."
        mkdir -p docker/postgres
        
        cat > docker/postgres/Dockerfile << 'EOF'
FROM postgres:15-alpine

# Install additional extensions
RUN apk add --no-cache postgresql-contrib

# Copy configuration files
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Set proper permissions
RUN chmod +x /docker-entrypoint-initdb.d/*.sql

# Expose port
EXPOSE 5432

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1
EOF
    fi
    
    # Create PostgreSQL config if not exists
    if [ ! -f "docker/postgres/postgresql.conf" ]; then
        cat > docker/postgres/postgresql.conf << 'EOF'
listen_addresses = '*'
port = 5432
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
EOF
    fi
    
    # Create pg_hba.conf if not exists
    if [ ! -f "docker/postgres/pg_hba.conf" ]; then
        cat > docker/postgres/pg_hba.conf << 'EOF'
local   all             postgres                                trust
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 trust
host    all             all             0.0.0.0/0               md5
EOF
    fi
    
    print_status "Docker setup files verified ✓"
}

# Function to test database setup
test_database_setup() {
    print_status "Testing database setup..."
    
    # Check if we can start the database
    if command -v docker-compose &> /dev/null; then
        print_status "Starting test database container..."
        
        # Start only postgres for testing
        docker-compose -f docker-compose.db.yml up -d postgres
        
        # Wait for database to be ready
        print_status "Waiting for database to be ready..."
        sleep 15
        
        # Test connection
        if docker-compose -f docker-compose.db.yml exec postgres pg_isready -U postgres > /dev/null 2>&1; then
            print_status "Database test successful ✓"
            
            # Test if atma_db exists
            if docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -lqt | cut -d \| -f 1 | grep -qw atma_db; then
                print_status "Database 'atma_db' created successfully ✓"
                
                # Test atma_user
                if docker-compose -f docker-compose.db.yml exec postgres psql -U postgres -d atma_db -c "SELECT 1 FROM pg_user WHERE usename = 'atma_user'" | grep -q "1 row"; then
                    print_status "User 'atma_user' created successfully ✓"
                else
                    print_warning "User 'atma_user' not found"
                fi
            else
                print_warning "Database 'atma_db' not found"
            fi
            
            # Stop test container
            docker-compose -f docker-compose.db.yml down
        else
            print_warning "Database test failed"
        fi
    else
        print_warning "Docker Compose not available for testing"
    fi
}

# Function to show completion summary
show_completion_summary() {
    print_header "Database Update Complete"
    
    echo -e "${GREEN}✅ Database update completed successfully!${NC}"
    echo ""
    echo -e "${GREEN}What was updated:${NC}"
    echo "• Exported current database to database_exports/"
    echo "• Updated Docker initialization scripts"
    echo "• Replaced init-database.sql with current data"
    echo "• Created backup of old init-database.sql"
    echo ""
    echo -e "${GREEN}Next steps:${NC}"
    echo "1. Test Docker setup:"
    echo "   ./scripts/setup-docker.sh"
    echo ""
    echo "2. Or test database only:"
    echo "   docker-compose -f docker-compose.db.yml up -d postgres"
    echo ""
    echo "3. Verify database content:"
    echo "   docker-compose exec postgres psql -U postgres -d atma_db"
    echo ""
    echo -e "${GREEN}Files created:${NC}"
    echo "• database_exports/ - Database exports"
    echo "• docker/postgres/init-scripts/ - Docker initialization"
    echo "• database_comparison_*.md - Update report"
    echo ""
    echo -e "${YELLOW}Note:${NC} Old init-database.sql backed up with timestamp"
}

# Main execution
main() {
    print_header "Update Docker Database Setup"
    
    # Check if export script exists
    if [ ! -f "scripts/export-current-database.sh" ]; then
        print_error "Export script not found!"
        print_status "Please ensure scripts/export-current-database.sh exists"
        exit 1
    fi
    
    # Run export if not already done
    if [ ! -d "database_exports" ] || [ -z "$(ls database_exports/complete_database_*.sql 2>/dev/null)" ]; then
        print_status "Running database export first..."
        chmod +x scripts/export-current-database.sh
        ./scripts/export-current-database.sh
    fi
    
    # Get latest export
    local latest_export=$(check_export_exists)
    
    # Backup old files
    backup_old_init_file
    
    # Update files
    update_docker_init_scripts "$latest_export"
    update_main_init_file "$latest_export"
    
    # Verify setup
    verify_docker_setup
    
    # Create report
    create_comparison_report
    
    # Test if possible
    test_database_setup
    
    # Show summary
    show_completion_summary
    
    print_status "Database update completed successfully! 🎉"
}

# Run main function
main "$@"
