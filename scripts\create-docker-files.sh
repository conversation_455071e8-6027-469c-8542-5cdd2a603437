#!/bin/bash

# =====================================================
# ATMA Backend - Create All Docker Files Script
# =====================================================
# This script creates all necessary Docker files and
# configurations for ATMA Backend
# =====================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}========================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}========================================${NC}"
}

# Function to create PostgreSQL Docker files
create_postgres_files() {
    print_status "Creating PostgreSQL Docker files..."
    
    mkdir -p docker/postgres/init-scripts
    
    # PostgreSQL Dockerfile
    cat > docker/postgres/Dockerfile << 'EOF'
FROM postgres:15-alpine

# Install additional extensions
RUN apk add --no-cache postgresql-contrib

# Copy configuration files
COPY postgresql.conf /etc/postgresql/postgresql.conf
COPY pg_hba.conf /etc/postgresql/pg_hba.conf

# Copy initialization scripts
COPY init-scripts/ /docker-entrypoint-initdb.d/

# Set proper permissions
RUN chmod +x /docker-entrypoint-initdb.d/*.sql

# Expose port
EXPOSE 5432

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD pg_isready -U $POSTGRES_USER -d $POSTGRES_DB || exit 1
EOF

    # PostgreSQL configuration
    cat > docker/postgres/postgresql.conf << 'EOF'
# Basic Settings
listen_addresses = '*'
port = 5432
max_connections = 200
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Logging
log_destination = 'stderr'
logging_collector = on
log_directory = 'pg_log'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'all'
log_min_duration_statement = 1000

# Performance
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100

# Locale
lc_messages = 'en_US.utf8'
lc_monetary = 'en_US.utf8'
lc_numeric = 'en_US.utf8'
lc_time = 'en_US.utf8'
default_text_search_config = 'pg_catalog.english'
EOF

    # PostgreSQL authentication
    cat > docker/postgres/pg_hba.conf << 'EOF'
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                trust
local   all             all                                     trust
host    all             all             127.0.0.1/32            trust
host    all             all             ::1/128                 trust
host    all             all             0.0.0.0/0               md5
EOF

    # Database creation script
    cat > docker/postgres/init-scripts/01-create-database.sql << 'EOF'
-- Create database
CREATE DATABASE atma_db;

-- Create application user
CREATE USER atma_user WITH PASSWORD 'atma_secure_password_2024';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE atma_db TO atma_user;

-- Connect to the database
\c atma_db;

-- Grant schema creation privileges
GRANT CREATE ON DATABASE atma_db TO atma_user;
ALTER USER atma_user CREATEDB;
EOF

    # Copy existing init-database.sql if it exists
    if [ -f "init-database.sql" ]; then
        cp init-database.sql docker/postgres/init-scripts/02-init-schema.sql
        
        # Add user permissions
        cat >> docker/postgres/init-scripts/02-init-schema.sql << 'EOF'

-- Grant schema usage to application user
GRANT USAGE ON SCHEMA public TO atma_user;
GRANT USAGE ON SCHEMA auth TO atma_user;
GRANT USAGE ON SCHEMA archive TO atma_user;
GRANT USAGE ON SCHEMA assessment TO atma_user;

-- Grant all privileges on schemas
GRANT ALL ON SCHEMA auth TO atma_user;
GRANT ALL ON SCHEMA archive TO atma_user;
GRANT ALL ON SCHEMA assessment TO atma_user;

-- Grant all privileges on all tables in schemas
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA assessment TO atma_user;

-- Grant all privileges on all sequences in schemas
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA auth TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA archive TO atma_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA assessment TO atma_user;

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON TABLES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON TABLES TO atma_user;

ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA auth GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA archive GRANT ALL ON SEQUENCES TO atma_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA assessment GRANT ALL ON SEQUENCES TO atma_user;
EOF
    fi

    # Initial data script
    cat > docker/postgres/init-scripts/03-init-data.sql << 'EOF'
\c atma_db;

-- Insert sample schools
INSERT INTO public.schools (name, address, city, province) VALUES
('SMA Negeri 1 Jakarta', 'Jl. Budi Kemuliaan No. 6', 'Jakarta Pusat', 'DKI Jakarta'),
('SMA Negeri 3 Bandung', 'Jl. Belitung No. 8', 'Bandung', 'Jawa Barat'),
('SMA Negeri 1 Surabaya', 'Jl. Wijaya Kusuma No. 48', 'Surabaya', 'Jawa Timur'),
('SMA Negeri 2 Yogyakarta', 'Jl. Bener No. 30', 'Yogyakarta', 'DI Yogyakarta'),
('SMA Negeri 1 Medan', 'Jl. Mistar No. 41', 'Medan', 'Sumatera Utara');

-- Insert default admin user
INSERT INTO auth.users (
    id, 
    username, 
    email, 
    password_hash, 
    user_type, 
    is_active, 
    token_balance
) VALUES (
    uuid_generate_v4(),
    'superadmin',
    '<EMAIL>',
    '$2b$10$8K1p/a0dclxKoNqIfrHb2eUHiNNa6d4fMhEA6bMfLODAOtfSdZWO2', -- admin123
    'superadmin',
    true,
    1000
);

-- Insert admin profile
INSERT INTO auth.user_profiles (
    user_id,
    full_name,
    school_origin,
    gender
) VALUES (
    (SELECT id FROM auth.users WHERE email = '<EMAIL>'),
    'System Administrator',
    'ATMA System',
    'male'
);
EOF

    print_status "PostgreSQL Docker files created ✓"
}

# Function to create service Dockerfiles
create_service_dockerfiles() {
    print_status "Creating service Dockerfiles..."
    
    # Base Dockerfile template
    local dockerfile_template='FROM node:18-alpine

WORKDIR /app

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs && chown -R nodejs:nodejs /app
USER nodejs

EXPOSE PORT_PLACEHOLDER

HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:PORT_PLACEHOLDER/health || exit 1

CMD ["npm", "start"]'

    # Create Dockerfiles for each service
    local services=("api-gateway:3000" "auth-service:3001" "archive-service:3002" "assessment-service:3003" "notification-service:3005")
    
    for service_port in "${services[@]}"; do
        local service="${service_port%:*}"
        local port="${service_port#*:}"
        
        if [ -d "$service" ]; then
            echo "$dockerfile_template" | sed "s/PORT_PLACEHOLDER/$port/g" > "$service/Dockerfile"
            print_status "Created Dockerfile for $service ✓"
        fi
    done
    
    # Special case for analysis-worker (no HTTP endpoint)
    if [ -d "analysis-worker" ]; then
        cat > analysis-worker/Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodejs -u 1001

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Create logs directory
RUN mkdir -p logs && chown -R nodejs:nodejs /app
USER nodejs

CMD ["npm", "start"]
EOF
        print_status "Created Dockerfile for analysis-worker ✓"
    fi
}

# Function to create .dockerignore files
create_dockerignore_files() {
    print_status "Creating .dockerignore files..."
    
    local dockerignore_content='node_modules
npm-debug.log
.git
.gitignore
README.md
.env
.env.*
.nyc_output
coverage
.vscode
.idea
*.log
logs/
data/
backups/
docker/
scripts/
testing/
docs/
*.md
Dockerfile*
docker-compose*.yml'

    # Global .dockerignore
    echo "$dockerignore_content" > .dockerignore
    
    # Service-specific .dockerignore files
    local services=("api-gateway" "auth-service" "archive-service" "assessment-service" "analysis-worker" "notification-service")
    
    for service in "${services[@]}"; do
        if [ -d "$service" ]; then
            echo "$dockerignore_content" > "$service/.dockerignore"
        fi
    done
    
    print_status ".dockerignore files created ✓"
}

# Function to create directories
create_directories() {
    print_status "Creating directory structure..."
    
    # Create main directories
    mkdir -p docker/{postgres,rabbitmq,nginx}
    mkdir -p data/{postgres,rabbitmq,redis}
    mkdir -p logs/{postgres,rabbitmq,redis,api-gateway,auth-service,archive-service,assessment-service,analysis-worker,notification-service}
    mkdir -p backups
    mkdir -p scripts
    
    # Set proper permissions
    chmod 755 data/{postgres,rabbitmq,redis}
    chmod 755 logs/*
    chmod 755 backups
    
    print_status "Directory structure created ✓"
}

# Function to make scripts executable
make_scripts_executable() {
    print_status "Making scripts executable..."
    
    chmod +x scripts/*.sh 2>/dev/null || true
    
    print_status "Scripts made executable ✓"
}

# Function to show completion summary
show_completion_summary() {
    print_header "Docker Files Creation Complete"
    
    echo -e "${GREEN}✅ All Docker files have been created successfully!${NC}"
    echo ""
    echo -e "${GREEN}Created Files:${NC}"
    echo "• docker/postgres/Dockerfile"
    echo "• docker/postgres/postgresql.conf"
    echo "• docker/postgres/pg_hba.conf"
    echo "• docker/postgres/init-scripts/*.sql"
    echo "• Service Dockerfiles (api-gateway, auth-service, etc.)"
    echo "• .dockerignore files"
    echo "• Directory structure"
    echo ""
    echo -e "${GREEN}Next Steps:${NC}"
    echo "1. Review and update .env file with your API keys"
    echo "2. Run: ./scripts/setup-docker.sh"
    echo "3. Or run database setup first: ./scripts/setup-database.sh"
    echo ""
    echo -e "${GREEN}Quick Start:${NC}"
    echo "chmod +x scripts/*.sh"
    echo "./scripts/setup-docker.sh"
}

# Main execution
main() {
    print_header "Creating Docker Files for ATMA Backend"
    
    create_directories
    create_postgres_files
    create_service_dockerfiles
    create_dockerignore_files
    make_scripts_executable
    show_completion_summary
    
    print_status "Docker files creation completed successfully! 🎉"
}

# Run main function
main "$@"
