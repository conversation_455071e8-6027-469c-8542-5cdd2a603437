# ATMA Backend - Rencana Dockerisasi dan Kontainerisasi

## 📋 Overview

Dokumen ini berisi rencana lengkap untuk dockerisasi sistem ATMA Backend yang terdiri dari 6 microservices dengan arsitektur event-driven menggunakan PostgreSQL dan RabbitMQ.

## 🏗️ Arsitektur Target

```
┌─────────────────────────────────────────────────────────────┐
│                    Docker Compose Stack                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ API Gateway │  │ Auth Service│  │Archive Svc  │         │
│  │   :3000     │  │    :3001    │  │    :3002    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
│                                                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Assessment   │  │Analysis     │  │Notification │         │
│  │Svc :3003    │  │Worker       │  │Svc :3005    │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │ PostgreSQL  │  │  RabbitMQ   │  │   Redis     │         │
│  │   :5432     │  │   :5672     │  │   :6379     │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

## 📁 Struktur File Docker

```
atma-backend/
├── docker-compose.yml              # Orchestrasi semua services
├── docker-compose.dev.yml          # Override untuk development
├── docker-compose.prod.yml         # Override untuk production
├── .dockerignore                   # Global docker ignore
├── Dockerfile.base                 # Base image untuk Node.js services
├── scripts/
│   ├── init-db.sh                 # Script inisialisasi database
│   ├── wait-for-it.sh             # Script untuk dependency waiting
│   └── health-check.sh            # Health check scripts
├── docker/
│   ├── postgres/
│   │   ├── Dockerfile             # Custom PostgreSQL image
│   │   ├── init-scripts/          # Database initialization scripts
│   │   └── postgresql.conf        # PostgreSQL configuration
│   ├── rabbitmq/
│   │   ├── Dockerfile             # Custom RabbitMQ image
│   │   └── rabbitmq.conf          # RabbitMQ configuration
│   └── nginx/                     # Load balancer (optional)
│       ├── Dockerfile
│       └── nginx.conf
├── api-gateway/
│   └── Dockerfile                 # API Gateway container
├── auth-service/
│   └── Dockerfile                 # Auth Service container
├── archive-service/
│   └── Dockerfile                 # Archive Service container
├── assessment-service/
│   └── Dockerfile                 # Assessment Service container
├── analysis-worker/
│   └── Dockerfile                 # Analysis Worker container
├── notification-service/
│   └── Dockerfile                 # Notification Service container
└── .env.docker                    # Environment variables untuk Docker
```

## 🐳 Strategi Dockerisasi

### 1. Base Image Strategy
- **Multi-stage builds** untuk optimasi ukuran image
- **Node.js Alpine** sebagai base image (ringan dan aman)
- **Shared base image** untuk konsistensi antar services

### 2. Service Containerization
- **Satu container per service** (microservice pattern)
- **Health checks** untuk setiap service
- **Graceful shutdown** handling
- **Resource limits** untuk setiap container

### 3. Database Strategy
- **PostgreSQL container** dengan persistent volume
- **Automatic database initialization** menggunakan init-database.sql
- **Database migrations** otomatis saat startup
- **Backup strategy** dengan volume mounting

### 4. Message Queue Strategy
- **RabbitMQ container** dengan management plugin
- **Persistent queues** dengan volume mounting
- **Cluster-ready configuration** untuk scaling

## 📋 Tahapan Implementasi

### Phase 1: Persiapan Infrastructure
1. ✅ **Setup Base Dockerfile**
   - Buat Dockerfile.base untuk Node.js services
   - Optimasi dengan multi-stage builds
   - Security hardening

2. ✅ **Database Container Setup**
   - Custom PostgreSQL Dockerfile
   - Volume configuration untuk persistence
   - Initialization scripts setup

3. ✅ **Message Queue Setup**
   - RabbitMQ container dengan management UI
   - Queue persistence configuration
   - User dan permission setup

### Phase 2: Service Containerization
4. ✅ **Containerize Core Services**
   - API Gateway Dockerfile
   - Auth Service Dockerfile
   - Archive Service Dockerfile

5. ✅ **Containerize Processing Services**
   - Assessment Service Dockerfile
   - Analysis Worker Dockerfile
   - Notification Service Dockerfile

### Phase 3: Orchestration
6. ✅ **Docker Compose Setup**
   - Main docker-compose.yml
   - Development override
   - Production override
   - Environment configuration

7. ✅ **Networking & Dependencies**
   - Service discovery setup
   - Dependency management (wait-for-it)
   - Health check implementation

### Phase 4: Production Readiness
8. ✅ **Monitoring & Logging**
   - Centralized logging setup
   - Health monitoring
   - Performance metrics

9. ✅ **Security & Optimization**
   - Security scanning
   - Image optimization
   - Resource limits tuning

## 🔧 Environment Configuration

### Development Environment
```yaml
# docker-compose.dev.yml
services:
  postgres:
    ports:
      - "5432:5432"  # Expose untuk debugging
  rabbitmq:
    ports:
      - "15672:15672"  # Management UI
  api-gateway:
    volumes:
      - ./api-gateway/src:/app/src  # Hot reload
```

### Production Environment
```yaml
# docker-compose.prod.yml
services:
  postgres:
    # No port exposure
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
  api-gateway:
    deploy:
      replicas: 2  # Load balancing
```

## 📊 Resource Planning

### Container Resource Allocation
| Service | CPU Limit | Memory Limit | Replicas (Prod) |
|---------|-----------|--------------|-----------------|
| API Gateway | 0.5 | 512MB | 2 |
| Auth Service | 0.3 | 256MB | 2 |
| Archive Service | 0.3 | 256MB | 1 |
| Assessment Service | 0.3 | 256MB | 1 |
| Analysis Worker | 0.5 | 512MB | 3 |
| Notification Service | 0.2 | 128MB | 1 |
| PostgreSQL | 1.0 | 2GB | 1 |
| RabbitMQ | 0.5 | 1GB | 1 |
| Redis | 0.2 | 256MB | 1 |

### Volume Requirements
- **PostgreSQL Data**: 10GB minimum
- **RabbitMQ Data**: 2GB minimum
- **Application Logs**: 5GB minimum
- **Redis Data**: 1GB minimum

## 🚀 Deployment Strategy

### Local Development
```bash
# Start development environment
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# View logs
docker-compose logs -f

# Scale specific service
docker-compose up -d --scale analysis-worker=3
```

### Production Deployment
```bash
# Production deployment
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Health check
docker-compose ps
curl http://localhost:3000/health
```

## 🔍 Monitoring & Maintenance

### Health Checks
- **Database connectivity** checks
- **Service availability** monitoring
- **Queue health** monitoring
- **Resource usage** tracking

### Backup Strategy
- **Automated database backups** (daily)
- **Configuration backups**
- **Log rotation** dan archival

## 🚀 Quick Start Guide

### 1. Persiapan Awal
```bash
# Clone atau pastikan Anda berada di direktori atma-backend
cd atma-backend

# Buat script executable
chmod +x scripts/*.sh
```

### 2. Setup Database PostgreSQL
```bash
# Jalankan script setup database
./scripts/setup-database.sh

# Atau setup manual jika diperlukan
docker-compose -f docker-compose.db.yml up -d postgres
```

### 3. Setup Lengkap Semua Services
```bash
# Setup lengkap dengan satu command
./scripts/setup-docker.sh

# Atau setup bertahap
./scripts/setup-docker.sh --skip-build  # Skip building images
./scripts/setup-docker.sh --dev         # Development mode
```

### 4. Verifikasi Deployment
```bash
# Check status semua containers
docker-compose ps

# Test API Gateway
curl http://localhost:3000/health

# Check logs
docker-compose logs -f api-gateway
```

### 5. Management Commands
```bash
# Stop semua services
docker-compose down

# Start services
docker-compose up -d

# Restart specific service
docker-compose restart auth-service

# Scale analysis worker
docker-compose up -d --scale analysis-worker=3

# Backup database
./scripts/backup-database.sh

# Restore database
./scripts/restore-database.sh
```

## 📋 File yang Sudah Dibuat

✅ **Dokumentasi**
- `DOCKER_PLAN.md` - Rencana dockerisasi lengkap
- `DATABASE_DOCKER_SETUP.md` - Panduan setup database PostgreSQL

✅ **Docker Configuration**
- `docker-compose.yml` - Orchestrasi semua services
- `.env.docker` - Template environment variables
- `Dockerfile.base` - Base image untuk Node.js services

✅ **Database Setup**
- `scripts/setup-database.sh` - Script setup database otomatis
- `docker/postgres/Dockerfile` - Custom PostgreSQL image
- `docker/postgres/init-scripts/` - Database initialization scripts

✅ **Management Scripts**
- `scripts/setup-docker.sh` - Setup lengkap semua komponen
- `scripts/backup-database.sh` - Backup database otomatis
- `scripts/restore-database.sh` - Restore database dari backup

## 📝 Next Steps

1. ✅ **File Setup Completed**: Semua file konfigurasi sudah dibuat
2. 🔄 **Ready for Execution**: Jalankan `./scripts/setup-docker.sh`
3. 🔧 **Customization**: Update `.env` dengan API keys dan passwords
4. 🧪 **Testing**: Test semua endpoints dan functionality
5. 🚀 **Production**: Deploy ke environment production

---

**Status**: ✅ Ready for Implementation
**Target Completion**: 1-2 hari (setup dan testing)
**Priority**: High - Siap untuk dijalankan
