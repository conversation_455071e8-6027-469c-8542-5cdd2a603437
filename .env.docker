# =====================================================
# ATMA Backend - Docker Environment Configuration
# =====================================================
# This file contains environment variables for Docker deployment
# Copy this to .env and modify values as needed
# =====================================================

# =====================================================
# Application Configuration
# =====================================================
NODE_ENV=production
LOG_LEVEL=info

# =====================================================
# Database Configuration
# =====================================================
DB_HOST=postgres
DB_PORT=5432
DB_NAME=atma_db
DB_USER=atma_user
DB_PASSWORD=atma_secure_password_2024
DB_DIALECT=postgres

# PostgreSQL Admin Configuration
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres_admin_password

# Database Pool Configuration
DB_POOL_MAX=25
DB_POOL_MIN=5
DB_POOL_ACQUIRE=60000
DB_POOL_IDLE=30000

# =====================================================
# JWT Configuration
# =====================================================
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
JWT_EXPIRES_IN=7d

# =====================================================
# Security Configuration
# =====================================================
BCRYPT_ROUNDS=10
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# =====================================================
# Service URLs (Internal Docker Network)
# =====================================================
AUTH_SERVICE_URL=http://auth-service:3001
ARCHIVE_SERVICE_URL=http://archive-service:3002
ASSESSMENT_SERVICE_URL=http://assessment-service:3003
NOTIFICATION_SERVICE_URL=http://notification-service:3005

# =====================================================
# RabbitMQ Configuration
# =====================================================
RABBITMQ_URL=amqp://atma_user:atma_rabbitmq_password@rabbitmq:5672/atma_vhost
RABBITMQ_USER=atma_user
RABBITMQ_PASSWORD=atma_rabbitmq_password
RABBITMQ_VHOST=atma_vhost

# Queue Configuration
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process
DEAD_LETTER_QUEUE=assessment_analysis_dlq
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# Event-Driven Architecture
EVENTS_EXCHANGE_NAME=atma_events_exchange
EVENTS_QUEUE_NAME_ASSESSMENTS=analysis_events_assessments
EVENTS_QUEUE_NAME_NOTIFICATIONS=analysis_events_notifications
CONSUMER_PREFETCH=10

# =====================================================
# Redis Configuration
# =====================================================
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=atma_redis_password

# =====================================================
# Google AI Configuration
# =====================================================
# Get your API key from Google AI Studio: https://makersuite.google.com/app/apikey
GOOGLE_AI_API_KEY=your_google_ai_api_key_here_get_from_google_ai_studio
GOOGLE_AI_MODEL=gemini-2.5-flash
AI_TEMPERATURE=0.7

# Mock AI Configuration (for testing without using paid Gemini API)
USE_MOCK_MODEL=false

# =====================================================
# Service Ports
# =====================================================
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
ARCHIVE_SERVICE_PORT=3002
ASSESSMENT_SERVICE_PORT=3003
NOTIFICATION_SERVICE_PORT=3005

# =====================================================
# Application Settings
# =====================================================
DEFAULT_TOKEN_BALANCE=5
ANALYSIS_TOKEN_COST=1

# =====================================================
# WebSocket Configuration
# =====================================================
CORS_ORIGIN=*
SOCKET_PING_TIMEOUT=60000
SOCKET_PING_INTERVAL=25000

# =====================================================
# Performance Configuration
# =====================================================
ASYNC_LAST_LOGIN=true
ENABLE_QUERY_CACHE=true
ENABLE_PERFORMANCE_MONITORING=true

# Idempotency Configuration
IDEMPOTENCY_ENABLED=true
IDEMPOTENCY_TTL_HOURS=24
IDEMPOTENCY_MAX_CACHE_SIZE=10000
IDEMPOTENCY_CLEANUP_INTERVAL_MINUTES=60

# =====================================================
# Logging Configuration
# =====================================================
LOG_FILE_API_GATEWAY=logs/api-gateway.log
LOG_FILE_AUTH_SERVICE=logs/auth-service.log
LOG_FILE_ARCHIVE_SERVICE=logs/archive-service.log
LOG_FILE_ASSESSMENT_SERVICE=logs/assessment-service.log
LOG_FILE_ANALYSIS_WORKER=logs/analysis-worker.log
LOG_FILE_NOTIFICATION_SERVICE=logs/notification-service.log

# =====================================================
# Health Check Configuration
# =====================================================
HEALTH_CHECK_INTERVAL=30s
HEALTH_CHECK_TIMEOUT=10s
HEALTH_CHECK_RETRIES=3
HEALTH_CHECK_START_PERIOD=60s

# =====================================================
# Docker Compose Configuration
# =====================================================
COMPOSE_PROJECT_NAME=atma-backend
COMPOSE_FILE=docker-compose.yml

# =====================================================
# Volume Paths
# =====================================================
POSTGRES_DATA_PATH=./data/postgres
RABBITMQ_DATA_PATH=./data/rabbitmq
REDIS_DATA_PATH=./data/redis
LOGS_PATH=./logs
BACKUPS_PATH=./backups

# =====================================================
# Development Overrides
# =====================================================
# Uncomment these for development environment
# NODE_ENV=development
# LOG_LEVEL=debug
# USE_MOCK_MODEL=true

# =====================================================
# Production Security Notes
# =====================================================
# IMPORTANT: Change these values in production:
# 1. All passwords and secrets
# 2. JWT_SECRET - use a strong, unique secret
# 3. INTERNAL_SERVICE_KEY - use a strong, unique key
# 4. Database passwords
# 5. RabbitMQ credentials
# 6. Redis password
# 7. GOOGLE_AI_API_KEY - add your real API key

# =====================================================
# Backup Configuration
# =====================================================
BACKUP_RETENTION_DAYS=7
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM

# =====================================================
# Monitoring Configuration
# =====================================================
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_HEALTH_CHECKS=true
