version: '3.8'

services:
  # =====================================================
  # Infrastructure Services
  # =====================================================
  
  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    container_name: atma-postgres
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres_admin_password
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./logs/postgres:/var/log/postgresql
      - ./backups:/backups
    ports:
      - "5432:5432"
    networks:
      - atma-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  rabbitmq:
    image: rabbitmq:3.12-management-alpine
    container_name: atma-rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: atma_user
      RABBITMQ_DEFAULT_PASS: atma_rabbitmq_password
      RABBITMQ_DEFAULT_VHOST: atma_vhost
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
      - ./logs/rabbitmq:/var/log/rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    networks:
      - atma-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  redis:
    image: redis:7-alpine
    container_name: atma-redis
    command: redis-server --appendonly yes --requirepass atma_redis_password
    volumes:
      - redis_data:/data
      - ./logs/redis:/var/log/redis
    ports:
      - "6379:6379"
    networks:
      - atma-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # =====================================================
  # Application Services
  # =====================================================

  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    container_name: atma-api-gateway
    environment:
      - NODE_ENV=production
      - PORT=3000
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ASSESSMENT_SERVICE_URL=http://assessment-service:3003
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
    volumes:
      - ./logs/api-gateway:/app/logs
    ports:
      - "3000:3000"
    networks:
      - atma-network
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    container_name: atma-auth-service
    environment:
      - NODE_ENV=production
      - PORT=3001
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=atma_user
      - DB_PASSWORD=atma_secure_password_2024
      - DB_DIALECT=postgres
      - DB_SCHEMA=auth
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - JWT_EXPIRES_IN=7d
      - BCRYPT_ROUNDS=10
      - DEFAULT_TOKEN_BALANCE=5
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
    volumes:
      - ./logs/auth-service:/app/logs
    networks:
      - atma-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  archive-service:
    build:
      context: ./archive-service
      dockerfile: Dockerfile
    container_name: atma-archive-service
    environment:
      - NODE_ENV=production
      - PORT=3002
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=atma_db
      - DB_USER=atma_user
      - DB_PASSWORD=atma_secure_password_2024
      - DB_DIALECT=postgres
      - DB_SCHEMA=archive
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
    volumes:
      - ./logs/archive-service:/app/logs
    networks:
      - atma-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  assessment-service:
    build:
      context: ./assessment-service
      dockerfile: Dockerfile
    container_name: atma-assessment-service
    environment:
      - NODE_ENV=production
      - PORT=3003
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - RABBITMQ_URL=amqp://atma_user:atma_rabbitmq_password@rabbitmq:5672/atma_vhost
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - ROUTING_KEY=analysis.process
      - AUTH_SERVICE_URL=http://auth-service:3001
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - ANALYSIS_TOKEN_COST=1
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
    volumes:
      - ./logs/assessment-service:/app/logs
    networks:
      - atma-network
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3003/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  analysis-worker:
    build:
      context: ./analysis-worker
      dockerfile: Dockerfile
    container_name: atma-analysis-worker
    environment:
      - NODE_ENV=production
      - RABBITMQ_URL=amqp://atma_user:atma_rabbitmq_password@rabbitmq:5672/atma_vhost
      - QUEUE_NAME=assessment_analysis
      - EXCHANGE_NAME=atma_exchange
      - ROUTING_KEY=analysis.process
      - DEAD_LETTER_QUEUE=assessment_analysis_dlq
      - GOOGLE_AI_API_KEY=${GOOGLE_AI_API_KEY}
      - GOOGLE_AI_MODEL=gemini-2.5-flash
      - AI_TEMPERATURE=0.7
      - USE_MOCK_MODEL=false
      - ARCHIVE_SERVICE_URL=http://archive-service:3002
      - NOTIFICATION_SERVICE_URL=http://notification-service:3005
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - LOG_LEVEL=info
    volumes:
      - ./logs/analysis-worker:/app/logs
    networks:
      - atma-network
    depends_on:
      rabbitmq:
        condition: service_healthy
      archive-service:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      replicas: 2

  notification-service:
    build:
      context: ./notification-service
      dockerfile: Dockerfile
    container_name: atma-notification-service
    environment:
      - NODE_ENV=production
      - PORT=3005
      - JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
      - INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production
      - CORS_ORIGIN=*
      - SOCKET_PING_TIMEOUT=60000
      - SOCKET_PING_INTERVAL=25000
      - RABBITMQ_URL=amqp://atma_user:atma_rabbitmq_password@rabbitmq:5672/atma_vhost
      - EVENTS_EXCHANGE_NAME=atma_events_exchange
      - EVENTS_QUEUE_NAME_NOTIFICATIONS=analysis_events_notifications
      - CONSUMER_PREFETCH=10
      - LOG_LEVEL=info
    volumes:
      - ./logs/notification-service:/app/logs
    ports:
      - "3005:3005"
    networks:
      - atma-network
    depends_on:
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3005/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

# =====================================================
# Volumes
# =====================================================

volumes:
  postgres_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres
  
  rabbitmq_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/rabbitmq
  
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis

# =====================================================
# Networks
# =====================================================

networks:
  atma-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
